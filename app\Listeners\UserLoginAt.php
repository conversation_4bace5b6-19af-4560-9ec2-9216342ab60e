<?php

namespace App\Listeners;

use App\Models\Organization\OrganizationUser;
use App\Models\Organization\OrganizationUserPermission;
use App\Models\Organization\TeamInvite;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UserLoginAt
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        try{
            $event->user->update([
                'last_login' => now(),
            ]);

            $already_registered_organization = OrganizationUser::query()
                ->where('user_id',$event->user->id)
                ->get()
                ->pluck('organization_id')->toArray();

            $team_invites = TeamInvite::query()
                ->withoutGlobalScopes()
                ->where('email',$event->user->email)
                ->get();

            $primaryOrganizationId = null;

            foreach ($team_invites as $invite){
                $isAlreadyMember = in_array($invite->organization_id, $already_registered_organization);

                if(!$isAlreadyMember){
                    //link organization and user
                    $org_user = OrganizationUser::create([
                        'user_id'         => $event->user->id,
                        'organization_id' => $invite->organization_id,
                    ]);

                    //save organization_user_id in OrganizationUserPermission table
                    OrganizationUserPermission::query()
                        ->where('team_invite_id',$invite->id)
                        ->update([
                            'organization_user_id'=>$org_user->id
                        ]);

                    // Set the first organization as primary for session
                    if (!$primaryOrganizationId) {
                        $primaryOrganizationId = $invite->organization_id;
                    }

                    Log::info("User {$event->user->email} added to organization {$invite->organization_id} via team invitation");
                }
            }

            // If user was added to organizations and no session is set, set the primary one
            if ($primaryOrganizationId && !session('organization_id')) {
                session(['organization_id' => $primaryOrganizationId]);
                Log::info("Set organization {$primaryOrganizationId} as active for user {$event->user->email}");
            }
        }
        catch(\Exception $e)
        {
            Log::info('ERROR IN TEAM INVITATION');
            Log::error($e);
        }
    }
}
