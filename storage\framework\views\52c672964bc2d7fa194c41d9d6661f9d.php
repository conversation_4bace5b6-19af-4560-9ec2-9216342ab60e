
<?php $__env->startSection('titles','Mapping Fields'); ?>
<?php $__env->startSection('content'); ?>

    <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => 'Mapping Products','description' => ''.e(trans('Mapping convertion of your all products')).'','links' => 'false','button' => 'false'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['buttonname' => 'null']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>

    <form action="<?php echo e($mapping_type == 'import' ? route('mappingfields.import') : route('mappingfields.export')); ?>" method="POST">
        <input type="hidden" name="template" value=""  class="import_input_template">
        <?php echo csrf_field(); ?>
        <div>
            <div class="d-flex justify-content-between">
                <div class="d-flex align-items-sm-center">
                    <h3 class="mb-0">
                        Templates
                    </h3>
                </div>
                <div class="position-relative me-2">
                    <button role="button" id="create_template" name="create_template" value="create" class="btn btn-primary">
                        <?php echo e(trans('products_export_step2.create_new_template')); ?>

                    </button>


                </div>
            </div>
            <hr style="width: 100%;border-top: 1px solid rgba(0,0,0,.1);margin-top:4px">


            <div class="mt-4">
                <p>Would you like to use one of your previously saved templates
                    for <?php echo e($templates->first()->type); ?>?</p>


                <div id="no_template_found" style="display: none">
                    <div class="d-flex flex-column ">
                        <div class="p-2 mt-5 mx-auto">
                            <img src="<?php echo e(URL::asset('/mapping-fields/icons/<EMAIL>')); ?>" class="img-fluid"
                                 width="150px" alt="empty page">
                        </div>
                        <div class="p-2 mx-auto">
                            <p class="Roboto">
                                You have not created any template yet.
                            </p>
                        </div>
                    </div>
                </div>


                <table id="mapping_templates_table" class="hover row-border" style="width:100%">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Modified</th>

                        <?php if($templates->first()->type == "export"): ?>
                            <th>Export Type</th>
                        <?php endif; ?>
                        <th class="text-end">Action</th>

                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="template-row"
                            data-temp-id="<?php echo e(isset($template->id) ? $template->id : Str::slug($template->name)); ?>"
                            data-temp-name="<?php echo e($template->name ?? null); ?>">

                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-folder-open temp-folder-icon me-3"></i>
                                    <span class="font-weight-bold"><?php echo e($template->name); ?></span>
                                </div>
                            </td>

                            <td><?php echo e(isset($template->updated_at) ? $template->updated_at->diffForHumans() : null); ?></td>
                            <?php if($templates->first()->type == "export"): ?>
                                <td><?php echo e($template->export_type); ?></td>
                            <?php endif; ?>
                            <td class="text-right">
                                <div class="d-flex align-items-center justify-content-end">
                                    <div>
                                        <a href="javascript:void(0)" type="button" class="edit_mapping_template text-primary text-decoration-none">Apply</a>
                                        <?php if(isset($template->id)): ?>
                                            <a href="javascript:void(0)" type="button" class="delete_mapping_template text-decoration-none ms-1"><i class="fa-regular fa-trash-can fs-20 text-danger" ></i></a>
                                        <?php endif; ?>
                                    </div>

                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>

                </table>
            </div>
        </div>



        

        
        
        
        
        
        
        
        
        
        
        
        
        
        

        
        
        

        

        

        
        
    </form>


    <!--VLookup Modal -->
    <?php if (isset($component)) { $__componentOriginal35b60d6919fe1770ebacbf5c4995d007 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal35b60d6919fe1770ebacbf5c4995d007 = $attributes; } ?>
<?php $component = Apimio\MappingConnectorPackage\components\alerts\TemplateDelete::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('component-template-delete'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\MappingConnectorPackage\components\alerts\TemplateDelete::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal35b60d6919fe1770ebacbf5c4995d007)): ?>
<?php $attributes = $__attributesOriginal35b60d6919fe1770ebacbf5c4995d007; ?>
<?php unset($__attributesOriginal35b60d6919fe1770ebacbf5c4995d007); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal35b60d6919fe1770ebacbf5c4995d007)): ?>
<?php $component = $__componentOriginal35b60d6919fe1770ebacbf5c4995d007; ?>
<?php unset($__componentOriginal35b60d6919fe1770ebacbf5c4995d007); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
    <script>

        $(document).ready(function () {


            var template_row = "";

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Default Configuration
            $(document).ready(function() {
                toastr.options = {
                    'closeButton': true,
                    'debug': false,
                    'newestOnTop': false,
                    'progressBar': true,
                    'positionClass': 'toast-top-right',
                    'preventDuplicates': false,
                    'showDuration': '1000',
                    'hideDuration': '1000',
                    'timeOut': '4000',
                    'extendedTimeOut': '1000',
                    'showEasing': 'swing',
                    'hideEasing': 'linear',
                    'showMethod': 'fadeIn',
                    'hideMethod': 'fadeOut',
                }
            });


            const mapping_templates_table = $('#mapping_templates_table').DataTable({
                responsive: true,
                order: [[ 1, 'desc' ]],
                // "pageLength": 10
            });

            $(document).on('click', ".edit_mapping_template",function (event) {
                event.preventDefault();
                const template_input_field = $('input[name=template]');
                template_input_field.val($(this).parents('.template-row').data('temp-id'));
                template_input_field.closest("form").submit();
            });

             $(document).on('click', ".delete_mapping_template" ,function (event) {
                event.preventDefault();
                template_row = $(this).parents('.template-row');
                $('input[name=template_delete]').val(template_row.data('temp-id'));
                $('#template_delete_name').html(template_row.data('temp-name'));
                $("#template_delete_alert").modal('show');
            });

            $('#template_delete_btn').on('click',function (event) {
                event.preventDefault();
                var self = this;
                var previous_value = $(self).html();
                var form = $(this).closest('form');
                $.ajax({
                    url: '<?php echo e(route('mapping_delete_template')); ?>',
                    type: "POST",
                    data: $(form).serialize(),
                    beforeSend: function () {
                        $(self).addClass('disabled');
                        $(self).html('<span class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true"></span>');
                    },
                    error: function (response) {
                        $('#template_delete_alert').modal('hide');
                        $(self).removeClass('disabled');
                        $(self).html(previous_value);
                        toastr.error(response.responseJSON.msg);
                    },
                    success: function (response) {
                        $('#template_delete_alert').modal('hide');
                        $(self).removeClass('disabled');
                        $(self).html(previous_value);
                        toastr.success(response.msg);
                        template_row.addClass("bg-danger");
                        var templates_count = $('#mapping_templates_table').find('.template-row').length;
                        if(templates_count <= 1){
                            $('#mapping_templates_table_wrapper').hide();
                            $('#no_template_found').show();
                        }
                        template_row.hide(1000, function(){
                            this.remove();
                        });


                    }
                });
            });

            // $(".apply_mapping_template").click(function () {
            //     $(this).parents('tr').find('td:first').trigger('click');
            // });

            
            
            
            
            
            
            
            
            

            

        });



    </script>
<?php $__env->stopPush(); ?>








<?php echo $__env->make('mapping::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\mapping-fields-package\src/views/index.blade.php ENDPATH**/ ?>