{"__meta": {"id": "01JYR4YPPA9BYA7Q2WYJ41EF2J", "datetime": "2025-06-27 07:34:40", "utime": **********.075599, "method": "POST", "uri": "/products/import", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:34:26] LOG.debug: ImportProducts Queue success for user 1", "message_html": null, "is_string": false, "label": "debug", "time": **********.701962, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.041685, "end": **********.075621, "duration": 16.03393578529358, "duration_str": "16.03s", "measures": [{"label": "Booting", "start": **********.041685, "relative_start": 0, "end": **********.839166, "relative_end": **********.839166, "duration": 0.****************, "duration_str": "797ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.839186, "relative_start": 0.****************, "end": **********.075623, "relative_end": 2.1457672119140625e-06, "duration": 15.**************, "duration_str": "15.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.857875, "relative_start": 0.****************, "end": **********.863487, "relative_end": **********.863487, "duration": 0.005611896514892578, "duration_str": "5.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.071831, "relative_start": 16.**************, "end": **********.072115, "relative_end": **********.072115, "duration": 0.00028395652770996094, "duration_str": "284μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "2x notifications::email", "param_count": null, "params": [], "start": **********.991693, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/notifications/email.blade.phpnotifications::email", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fnotifications%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "notifications::email"}, {"name": "1x mail::message", "param_count": null, "params": [], "start": 1751009669.107784, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/html/message.blade.phpmail::message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::message"}, {"name": "1x mail::header", "param_count": null, "params": [], "start": 1751009669.167818, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/html/header.blade.phpmail::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::header"}, {"name": "1x mail::footer", "param_count": null, "params": [], "start": 1751009669.195719, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/html/footer.blade.phpmail::footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::footer"}, {"name": "1x mail::layout", "param_count": null, "params": [], "start": 1751009673.644855, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/html/layout.blade.phpmail::layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::layout"}, {"name": "1x mail::themes.default", "param_count": null, "params": [], "start": 1751009674.780347, "type": "css", "hash": "cssC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/html/themes/default.cssmail::themes.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fthemes%2Fdefault.css&line=1", "ajax": false, "filename": "default.css", "line": "?"}, "render_count": 1, "name_original": "mail::themes.default"}, {"name": "1x mail::message", "param_count": null, "params": [], "start": 1751009675.334063, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/text/message.blade.phpmail::message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::message"}, {"name": "1x mail::header", "param_count": null, "params": [], "start": 1751009675.390244, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/text/header.blade.phpmail::header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::header"}, {"name": "1x mail::footer", "param_count": null, "params": [], "start": 1751009675.41183, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/text/footer.blade.phpmail::footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::footer"}, {"name": "1x mail::layout", "param_count": null, "params": [], "start": 1751009675.431308, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/vendor/mail/text/layout.blade.phpmail::layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "mail::layout"}]}, "route": {"uri": "POST products/import", "middleware": "web, check_billing, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ImportController@import_csv<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=644\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "as": "products.import", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=644\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ImportController.php:644-818</a>"}, "queries": {"count": 14, "nb_statements": 12, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04774, "accumulated_duration_str": "47.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.9000921, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 7.227}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9160998, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.227, "width_percent": 1.257}, {"sql": "select * from `organizations` where `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 87}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9304671, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.483, "width_percent": 2.493}, {"sql": "select `products`.`sku` as `product_handle`, GROUP_CONCAT(variants.sku) as variant_skus from `products` left join `variants` on `products`.`id` = `variants`.`product_id` where `products`.`organization_id` = '1' and `organization_id` = '1' group by `products`.`sku`", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 669}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.679584, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ImportController.php:669", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 669}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=669", "ajax": false, "filename": "ImportController.php", "line": "669"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.976, "width_percent": 2.346}, {"sql": "insert into `notifications` (`id`, `user_id`, `organization_id`, `type`, `data`, `read_at`, `notifiable_id`, `notifiable_type`, `updated_at`, `created_at`) values ('fe48e0e5-847d-462e-8955-10e4c4cb6209', 1, '1', 'App\\\\Notifications\\\\ApimioNotification', '[{\\\"subject\\\":\\\"Import CSV Products Queue\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your CSV file is now being processed by Apimio. We\\\\u2019ll notify you once all product data has been successfully imported.\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"View\\\",\\\"actionURL\\\":\\\"http:\\\\/\\\\/localhost:8000\\\\/products\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\",\\\"batch\\\":null}]', null, 1, 'App\\\\User', '2025-06-27 07:34:39', '2025-06-27 07:34:39')", "type": "query", "params": [], "bindings": ["fe48e0e5-847d-462e-8955-10e4c4cb6209", 1, "1", "App\\Notifications\\ApimioNotification", "[{\"subject\":\"Import CSV Products Queue\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your CSV file is now being processed by Apimio. We\\u2019ll notify you once all product data has been successfully imported.\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"View\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\",\"user_id\":1,\"organization_id\":\"1\",\"batch\":null}]", null, 1, "App\\User", "2025-06-27 07:34:39", "2025-06-27 07:34:39"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Notifications/DBCustomChannel.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Notifications\\DBCustomChannel.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 106}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 101}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Notifications/NotificationSender.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationSender.php", "line": 79}], "start": **********.567373, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "DBCustomChannel.php:25", "source": {"index": 16, "namespace": null, "name": "app/Notifications/DBCustomChannel.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Notifications\\DBCustomChannel.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FNotifications%2FDBCustomChannel.php&line=25", "ajax": false, "filename": "DBCustomChannel.php", "line": "25"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.322, "width_percent": 12.442}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/ImportExport.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\ImportExport.php", "line": 936}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 736}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.578135, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "ImportExport.php:936", "source": {"index": 20, "namespace": null, "name": "app/Classes/ImportExport.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\ImportExport.php", "line": 936}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FImportExport.php&line=936", "ajax": false, "filename": "ImportExport.php", "line": "936"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.765, "width_percent": 10.411}, {"sql": "insert into `job_batches` (`id`, `name`, `total_jobs`, `pending_jobs`, `failed_jobs`, `failed_job_ids`, `options`, `created_at`, `cancelled_at`, `finished_at`) values ('9f40defe-f107-453e-9f00-fc7223b0ae3c', '', 0, 0, 0, '[]', 'a:1:{s:4:\\\"then\\\";a:1:{i:0;O:47:\\\"Laravel\\SerializableClosure\\SerializableClosure\\\":1:{s:12:\\\"serializable\\\";O:46:\\\"Lara<PERSON>\\SerializableClosure\\Serializers\\Signed\\\":2:{s:12:\\\"serializable\\\";s:22529:\\\"O:46:\\\"Lara<PERSON>\\SerializableClosure\\Serializers\\Native\\\":5:{s:3:\\\"use\\\";a:1:{s:4:\\\"data\\\";a:6:{s:8:\\\"all_data\\\";a:15:{s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:20:\\\"template_method_type\\\";s:6:\\\"import\\\";s:5:\\\"nodes\\\";a:1:{s:4:\\\"data\\\";a:4:{i:0;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:20:\\\"Default,Product Name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:14:\\\"Default,handle\\\";}s:2:\\\"id\\\";s:36:\\\"2249526f-665d-4bbf-9125-65742334829b\\\";}i:1;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"Default,SKU\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Variant,sku\\\";}s:2:\\\"id\\\";s:36:\\\"694a4d9c-2ead-41c6-accb-e20750e9e972\\\";}i:2;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Default,Price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:13:\\\"Variant,price\\\";}s:2:\\\"id\\\";s:36:\\\"c381d306-5a4d-4a40-bc3a-cee8403559e3\\\";}i:3;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:16:\\\"Default,Quantity\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:18:\\\"Default,categories\\\";}s:2:\\\"id\\\";s:36:\\\"7d3dcbfb-172b-498b-a89d-c1a632ce2c3c\\\";}}}s:7:\\\"version\\\";s:1:\\\"1\\\";s:7:\\\"catalog\\\";a:1:{i:0;s:1:\\\"1\\\";}s:6:\\\"status\\\";s:1:\\\"1\\\";s:11:\\\"temp_status\\\";s:3:\\\"off\\\";s:9:\\\"temp_name\\\";s:14:\\\"Apimio Default\\\";s:7:\\\"temp_id\\\";N;s:11:\\\"export_type\\\";N;s:13:\\\"import_action\\\";s:1:\\\"3\\\";s:9:\\\"file_path\\\";s:50:\\\"mapping_fields/upload/json/1751009623_datafile.csv\\\";s:15:\\\"ignore_unmapped\\\";s:3:\\\"off\\\";s:16:\\\"all_product_skus\\\";a:1:{s:15:\\\"testing-product\\\";a:0:{}}s:12:\\\"request_data\\\";a:11:{s:11:\\\"input_array\\\";a:2:{s:10:\\\"array_name\\\";s:3:\\\"CSV\\\";s:5:\\\"nodes\\\";a:1:{i:0;a:2:{s:4:\\\"name\\\";s:7:\\\"Default\\\";s:10:\\\"attributes\\\";a:4:{s:12:\\\"Product Name\\\";s:12:\\\"Product Name\\\";s:3:\\\"SKU\\\";s:3:\\\"SKU\\\";s:5:\\\"Price\\\";s:5:\\\"Price\\\";s:8:\\\"Quantity\\\";s:8:\\\"Quantity\\\";}}}}s:21:\\\"converted_input_array\\\";a:1:{i:0;a:3:{s:5:\\\"label\\\";s:7:\\\"Default\\\";s:5:\\\"title\\\";s:7:\\\"Default\\\";s:7:\\\"options\\\";a:4:{i:0;a:2:{s:5:\\\"label\\\";s:12:\\\"Product Name\\\";s:5:\\\"value\\\";s:20:\\\"Default,Product Name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:3:\\\"SKU\\\";s:5:\\\"value\\\";s:11:\\\"Default,SKU\\\";}i:2;a:2:{s:5:\\\"label\\\";s:5:\\\"Price\\\";s:5:\\\"value\\\";s:13:\\\"Default,Price\\\";}i:3;a:2:{s:5:\\\"label\\\";s:8:\\\"Quantity\\\";s:5:\\\"value\\\";s:16:\\\"Default,Quantity\\\";}}}}s:9:\\\"file_path\\\";s:50:\\\"mapping_fields/upload/json/1751009623_datafile.csv\\\";s:8:\\\"file_url\\\";s:100:\\\"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751009623_datafile.csv\\\";s:13:\\\"data_required\\\";a:8:{s:20:\\\"template_method_type\\\";s:6:\\\"import\\\";s:11:\\\"output_type\\\";s:6:\\\"Apimio\\\";s:4:\\\"sync\\\";b:0;s:18:\\\"redirect_url_route\\\";s:15:\\\"products.import\\\";s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:8:\\\"versions\\\";a:1:{i:1;s:5:\\\"EN-US\\\";}s:8:\\\"catalogs\\\";a:1:{i:1;s:13:\\\"Tanzayb Store\\\";}s:13:\\\"import_action\\\";s:1:\\\"3\\\";}s:13:\\\"import_action\\\";s:1:\\\"3\\\";s:26:\\\"apimio_attributes_required\\\";a:2:{s:12:\\\"all_families\\\";a:0:{}s:14:\\\"all_attributes\\\";a:6:{i:0;a:2:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:16:\\\"single line text\\\";}i:1;a:2:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:6:\\\"number\\\";}i:2;a:2:{s:2:\\\"id\\\";i:3;s:4:\\\"name\\\";s:15:\\\"multi line text\\\";}i:3;a:2:{s:2:\\\"id\\\";i:7;s:4:\\\"name\\\";s:11:\\\"measurement\\\";}i:4;a:2:{s:2:\\\"id\\\";i:9;s:4:\\\"name\\\";s:4:\\\"json\\\";}i:5;a:2:{s:2:\\\"id\\\";i:11;s:4:\\\"name\\\";s:3:\\\"url\\\";}}}s:19:\\\"template_attributes\\\";a:0:{}s:12:\\\"output_array\\\";a:2:{s:10:\\\"array_name\\\";s:6:\\\"Apimio\\\";s:5:\\\"nodes\\\";a:6:{i:0;a:2:{s:4:\\\"name\\\";s:7:\\\"Default\\\";s:10:\\\"attributes\\\";a:5:{s:6:\\\"handle\\\";s:18:\\\"Product Identifier\\\";s:4:\\\"file\\\";s:14:\\\"Product Images\\\";s:6:\\\"vendor\\\";s:6:\\\"Vendor\\\";s:5:\\\"brand\\\";s:5:\\\"Brand\\\";s:10:\\\"categories\\\";s:8:\\\"Category\\\";}}i:1;a:2:{s:4:\\\"name\\\";s:7:\\\"General\\\";s:10:\\\"attributes\\\";a:2:{s:12:\\\"product_name\\\";s:12:\\\"Product Name\\\";s:11:\\\"description\\\";s:11:\\\"Description\\\";}}i:2;a:2:{s:4:\\\"name\\\";s:7:\\\"Variant\\\";s:10:\\\"attributes\\\";a:11:{s:3:\\\"sku\\\";s:3:\\\"SKU\\\";s:4:\\\"name\\\";s:4:\\\"Name\\\";s:4:\\\"file\\\";s:5:\\\"Image\\\";s:5:\\\"price\\\";s:5:\\\"Price\\\";s:16:\\\"compare_at_price\\\";s:16:\\\"Compare at Price\\\";s:10:\\\"cost_price\\\";s:10:\\\"Cost Price\\\";s:7:\\\"barcode\\\";s:13:\\\"UPC / Barcode\\\";s:6:\\\"weight\\\";s:6:\\\"Weight\\\";s:11:\\\"weight_unit\\\";s:11:\\\"Weight Unit\\\";s:14:\\\"track_quantity\\\";s:14:\\\"Track Quantity\\\";s:16:\\\"continue_selling\\\";s:16:\\\"Continue Selling\\\";}}i:3;a:2:{s:4:\\\"name\\\";s:14:\\\"Variant Option\\\";s:10:\\\"attributes\\\";a:6:{s:12:\\\"option1_name\\\";s:13:\\\"Option 1 Name\\\";s:13:\\\"option1_value\\\";s:14:\\\"Option 1 Value\\\";s:12:\\\"option2_name\\\";s:13:\\\"Option 2 Name\\\";s:13:\\\"option2_value\\\";s:14:\\\"Option 2 Value\\\";s:12:\\\"option3_name\\\";s:13:\\\"Option 3 Name\\\";s:13:\\\"option3_value\\\";s:14:\\\"Option 3 Value\\\";}}i:4;a:2:{s:4:\\\"name\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:10:\\\"attributes\\\";a:1:{i:1;s:23:\\\"Tanzayb Store Warehouse\\\";}}i:5;a:2:{s:4:\\\"name\\\";s:3:\\\"SEO\\\";s:10:\\\"attributes\\\";a:4:{s:7:\\\"seo_url\\\";s:8:\\\"URL Slug\\\";s:9:\\\"seo_title\\\";s:9:\\\"SEO Title\\\";s:15:\\\"seo_description\\\";s:15:\\\"SEO Description\\\";s:11:\\\"seo_keyword\\\";s:4:\\\"Tags\\\";}}}}s:22:\\\"converted_output_array\\\";a:6:{i:0;a:3:{s:5:\\\"label\\\";s:7:\\\"Default\\\";s:5:\\\"title\\\";s:7:\\\"Default\\\";s:7:\\\"options\\\";a:5:{i:0;a:2:{s:5:\\\"label\\\";s:18:\\\"Product Identifier\\\";s:5:\\\"value\\\";s:14:\\\"Default,handle\\\";}i:1;a:2:{s:5:\\\"label\\\";s:14:\\\"Product Images\\\";s:5:\\\"value\\\";s:12:\\\"Default,file\\\";}i:2;a:2:{s:5:\\\"label\\\";s:6:\\\"Vendor\\\";s:5:\\\"value\\\";s:14:\\\"Default,vendor\\\";}i:3;a:2:{s:5:\\\"label\\\";s:5:\\\"Brand\\\";s:5:\\\"value\\\";s:13:\\\"Default,brand\\\";}i:4;a:2:{s:5:\\\"label\\\";s:8:\\\"Category\\\";s:5:\\\"value\\\";s:18:\\\"Default,categories\\\";}}}i:1;a:3:{s:5:\\\"label\\\";s:7:\\\"General\\\";s:5:\\\"title\\\";s:7:\\\"General\\\";s:7:\\\"options\\\";a:2:{i:0;a:2:{s:5:\\\"label\\\";s:12:\\\"Product Name\\\";s:5:\\\"value\\\";s:20:\\\"General,product_name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:11:\\\"Description\\\";s:5:\\\"value\\\";s:19:\\\"General,description\\\";}}}i:2;a:3:{s:5:\\\"label\\\";s:7:\\\"Variant\\\";s:5:\\\"title\\\";s:7:\\\"Variant\\\";s:7:\\\"options\\\";a:11:{i:0;a:2:{s:5:\\\"label\\\";s:3:\\\"SKU\\\";s:5:\\\"value\\\";s:11:\\\"Variant,sku\\\";}i:1;a:2:{s:5:\\\"label\\\";s:4:\\\"Name\\\";s:5:\\\"value\\\";s:12:\\\"Variant,name\\\";}i:2;a:2:{s:5:\\\"label\\\";s:5:\\\"Image\\\";s:5:\\\"value\\\";s:12:\\\"Variant,file\\\";}i:3;a:2:{s:5:\\\"label\\\";s:5:\\\"Price\\\";s:5:\\\"value\\\";s:13:\\\"Variant,price\\\";}i:4;a:2:{s:5:\\\"label\\\";s:16:\\\"Compare at Price\\\";s:5:\\\"value\\\";s:24:\\\"Variant,compare_at_price\\\";}i:5;a:2:{s:5:\\\"label\\\";s:10:\\\"Cost Price\\\";s:5:\\\"value\\\";s:18:\\\"Variant,cost_price\\\";}i:6;a:2:{s:5:\\\"label\\\";s:13:\\\"UPC / Barcode\\\";s:5:\\\"value\\\";s:15:\\\"Variant,barcode\\\";}i:7;a:2:{s:5:\\\"label\\\";s:6:\\\"Weight\\\";s:5:\\\"value\\\";s:14:\\\"Variant,weight\\\";}i:8;a:2:{s:5:\\\"label\\\";s:11:\\\"Weight Unit\\\";s:5:\\\"value\\\";s:19:\\\"Variant,weight_unit\\\";}i:9;a:2:{s:5:\\\"label\\\";s:14:\\\"Track Quantity\\\";s:5:\\\"value\\\";s:22:\\\"Variant,track_quantity\\\";}i:10;a:2:{s:5:\\\"label\\\";s:16:\\\"Continue Selling\\\";s:5:\\\"value\\\";s:24:\\\"Variant,continue_selling\\\";}}}i:3;a:3:{s:5:\\\"label\\\";s:14:\\\"Variant Option\\\";s:5:\\\"title\\\";s:14:\\\"Variant Option\\\";s:7:\\\"options\\\";a:6:{i:0;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 1 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option1_name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 1 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option1_value\\\";}i:2;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 2 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option2_name\\\";}i:3;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 2 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option2_value\\\";}i:4;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 3 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option3_name\\\";}i:5;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 3 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option3_value\\\";}}}i:4;a:3:{s:5:\\\"label\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:5:\\\"title\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:7:\\\"options\\\";a:1:{i:0;a:2:{s:5:\\\"label\\\";s:23:\\\"Tanzayb Store Warehouse\\\";s:5:\\\"value\\\";s:28:\\\"Inventory -> Tanzayb Store,1\\\";}}}i:5;a:3:{s:5:\\\"label\\\";s:3:\\\"SEO\\\";s:5:\\\"title\\\";s:3:\\\"SEO\\\";s:7:\\\"options\\\";a:4:{i:0;a:2:{s:5:\\\"label\\\";s:8:\\\"URL Slug\\\";s:5:\\\"value\\\";s:11:\\\"SEO,seo_url\\\";}i:1;a:2:{s:5:\\\"label\\\";s:9:\\\"SEO Title\\\";s:5:\\\"value\\\";s:13:\\\"SEO,seo_title\\\";}i:2;a:2:{s:5:\\\"label\\\";s:15:\\\"SEO Description\\\";s:5:\\\"value\\\";s:19:\\\"SEO,seo_description\\\";}i:3;a:2:{s:5:\\\"label\\\";s:4:\\\"Tags\\\";s:5:\\\"value\\\";s:15:\\\"SEO,seo_keyword\\\";}}}}s:12:\\\"mapping_data\\\";a:4:{i:0;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:20:\\\"Default,Product Name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:20:\\\"General,product_name\\\";}}i:1;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"Default,SKU\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Variant,sku\\\";}}i:2;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Default,Price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:13:\\\"Variant,price\\\";}}i:3;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:16:\\\"Default,Quantity\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:0:{}}}}}s:4:\\\"user\\\";O:8:\\\"App\\User\\\":32:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:5:\\\"users\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-27 06:52:55\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 06:52:55\\\";}s:11:\\\"?*?original\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-27 06:52:55\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 06:52:55\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:8:\\\"?*?casts\\\";a:1:{s:17:\\\"email_verified_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:2:{s:19:\\\"unreadNotifications\\\";O:55:\\\"Illuminate\\Notifications\\DatabaseNotificationCollection\\\":2:{s:8:\\\"?*?items\\\";a:0:{}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}s:13:\\\"notifications\\\";O:55:\\\"Illuminate\\Notifications\\DatabaseNotificationCollection\\\":2:{s:8:\\\"?*?items\\\";a:4:{i:0;O:45:\\\"Illuminate\\Notifications\\DatabaseNotification\\\":30:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:13:\\\"notifications\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:364:\\\"[{\\\"subject\\\":\\\"Import CSV Products Queue\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your CSV file is now being processed by Apimio. We\\u2019ll notify you once all product data has been successfully imported.\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"View\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\",\\\"batch\\\":null}]\\\";s:7:\\\"read_at\\\";N;s:10:\\\"created_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";}s:11:\\\"?*?original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:364:\\\"[{\\\"subject\\\":\\\"Import CSV Products Queue\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your CSV file is now being processed by Apimio. We\\u2019ll notify you once all product data has been successfully imported.\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"View\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\",\\\"batch\\\":null}]\\\";s:7:\\\"read_at\\\";N;s:10:\\\"created_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:0:{}}i:1;O:45:\\\"Illuminate\\Notifications\\DatabaseNotification\\\":30:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:13:\\\"notifications\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:724:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\"}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:57\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"?*?original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:724:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\"}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:57\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:0:{}}i:2;O:45:\\\"Illuminate\\Notifications\\DatabaseNotification\\\":30:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:13:\\\"notifications\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"?*?original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:0:{}}i:3;O:45:\\\"Illuminate\\Notifications\\DatabaseNotification\\\":30:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:13:\\\"notifications\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:52\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"?*?original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\Notifications\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:52\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"?*?changes\\\";a:0:{}s:8:\\\"?*?casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:0:{}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}}s:10:\\\"?*?touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:2:{i:0;s:8:\\\"password\\\";i:1;s:14:\\\"remember_token\\\";}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:9:{i:0;s:5:\\\"fname\\\";i:1;s:5:\\\"lname\\\";i:2;s:5:\\\"email\\\";i:3;s:8:\\\"password\\\";i:4;s:17:\\\"verification_code\\\";i:5;s:5:\\\"phone\\\";i:6;s:17:\\\"email_verified_at\\\";i:7;s:15:\\\"shopify_shop_id\\\";i:8;s:10:\\\"last_login\\\";}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}s:20:\\\"?*?rememberTokenName\\\";s:14:\\\"remember_token\\\";s:14:\\\"?*?accessToken\\\";N;}s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:8:\\\"filename\\\";s:48:\\\"temp_files/error_products_2025_06_27_073426.xlsx\\\";s:5:\\\"batch\\\";N;s:15:\\\"notification_id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";}}s:8:\\\"function\\\";s:172:\\\"function (\\Illuminate\\Bus\\Batch $batch) use ($data) {\\r\\n                        \\Illuminate\\Support\\Facades\\Log::channel(\\'mapping\\')->info(\\\"job done\\\");\\r\\n                    }\\\";s:5:\\\"scope\\\";s:45:\\\"App\\Http\\Controllers\\Product\\ImportController\\\";s:4:\\\"this\\\";N;s:4:\\\"self\\\";s:32:\\\"0000000000000bf10000000000000000\\\";}\\\";s:4:\\\"hash\\\";s:44:\\\"6YRyYZ0gejBiXV/3oIS4mZ4N6Tb3KdYXx9d50H0vxe8=\\\";}}}}', **********, '', '')", "type": "query", "params": [], "bindings": ["9f40defe-f107-453e-9f00-fc7223b0ae3c", "", 0, 0, 0, "[]", "a:1:{s:4:\"then\";a:1:{i:0;O:47:\"<PERSON><PERSON>\\SerializableClosure\\SerializableClosure\":1:{s:12:\"serializable\";O:46:\"<PERSON><PERSON>\\SerializableClosure\\Serializers\\Signed\":2:{s:12:\"serializable\";s:22529:\"O:46:\"<PERSON><PERSON>\\SerializableClosure\\Serializers\\Native\":5:{s:3:\"use\";a:1:{s:4:\"data\";a:6:{s:8:\"all_data\";a:15:{s:15:\"organization_id\";s:1:\"1\";s:20:\"template_method_type\";s:6:\"import\";s:5:\"nodes\";a:1:{s:4:\"data\";a:4:{i:0;a:4:{s:4:\"from\";a:1:{i:0;s:20:\"Default,Product Name\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:14:\"Default,handle\";}s:2:\"id\";s:36:\"2249526f-665d-4bbf-9125-65742334829b\";}i:1;a:4:{s:4:\"from\";a:1:{i:0;s:11:\"Default,SKU\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:11:\"Variant,sku\";}s:2:\"id\";s:36:\"694a4d9c-2ead-41c6-accb-e20750e9e972\";}i:2;a:4:{s:4:\"from\";a:1:{i:0;s:13:\"Default,Price\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:13:\"Variant,price\";}s:2:\"id\";s:36:\"c381d306-5a4d-4a40-bc3a-cee8403559e3\";}i:3;a:4:{s:4:\"from\";a:1:{i:0;s:16:\"Default,Quantity\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:18:\"Default,categories\";}s:2:\"id\";s:36:\"7d3dcbfb-172b-498b-a89d-c1a632ce2c3c\";}}}s:7:\"version\";s:1:\"1\";s:7:\"catalog\";a:1:{i:0;s:1:\"1\";}s:6:\"status\";s:1:\"1\";s:11:\"temp_status\";s:3:\"off\";s:9:\"temp_name\";s:14:\"Apimio Default\";s:7:\"temp_id\";N;s:11:\"export_type\";N;s:13:\"import_action\";s:1:\"3\";s:9:\"file_path\";s:50:\"mapping_fields/upload/json/1751009623_datafile.csv\";s:15:\"ignore_unmapped\";s:3:\"off\";s:16:\"all_product_skus\";a:1:{s:15:\"testing-product\";a:0:{}}s:12:\"request_data\";a:11:{s:11:\"input_array\";a:2:{s:10:\"array_name\";s:3:\"CSV\";s:5:\"nodes\";a:1:{i:0;a:2:{s:4:\"name\";s:7:\"Default\";s:10:\"attributes\";a:4:{s:12:\"Product Name\";s:12:\"Product Name\";s:3:\"SKU\";s:3:\"SKU\";s:5:\"Price\";s:5:\"Price\";s:8:\"Quantity\";s:8:\"Quantity\";}}}}s:21:\"converted_input_array\";a:1:{i:0;a:3:{s:5:\"label\";s:7:\"Default\";s:5:\"title\";s:7:\"Default\";s:7:\"options\";a:4:{i:0;a:2:{s:5:\"label\";s:12:\"Product Name\";s:5:\"value\";s:20:\"Default,Product Name\";}i:1;a:2:{s:5:\"label\";s:3:\"SKU\";s:5:\"value\";s:11:\"Default,SKU\";}i:2;a:2:{s:5:\"label\";s:5:\"Price\";s:5:\"value\";s:13:\"Default,Price\";}i:3;a:2:{s:5:\"label\";s:8:\"Quantity\";s:5:\"value\";s:16:\"Default,Quantity\";}}}}s:9:\"file_path\";s:50:\"mapping_fields/upload/json/1751009623_datafile.csv\";s:8:\"file_url\";s:100:\"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751009623_datafile.csv\";s:13:\"data_required\";a:8:{s:20:\"template_method_type\";s:6:\"import\";s:11:\"output_type\";s:6:\"Apimio\";s:4:\"sync\";b:0;s:18:\"redirect_url_route\";s:15:\"products.import\";s:15:\"organization_id\";s:1:\"1\";s:8:\"versions\";a:1:{i:1;s:5:\"EN-US\";}s:8:\"catalogs\";a:1:{i:1;s:13:\"Tanzayb Store\";}s:13:\"import_action\";s:1:\"3\";}s:13:\"import_action\";s:1:\"3\";s:26:\"apimio_attributes_required\";a:2:{s:12:\"all_families\";a:0:{}s:14:\"all_attributes\";a:6:{i:0;a:2:{s:2:\"id\";i:1;s:4:\"name\";s:16:\"single line text\";}i:1;a:2:{s:2:\"id\";i:2;s:4:\"name\";s:6:\"number\";}i:2;a:2:{s:2:\"id\";i:3;s:4:\"name\";s:15:\"multi line text\";}i:3;a:2:{s:2:\"id\";i:7;s:4:\"name\";s:11:\"measurement\";}i:4;a:2:{s:2:\"id\";i:9;s:4:\"name\";s:4:\"json\";}i:5;a:2:{s:2:\"id\";i:11;s:4:\"name\";s:3:\"url\";}}}s:19:\"template_attributes\";a:0:{}s:12:\"output_array\";a:2:{s:10:\"array_name\";s:6:\"Apimio\";s:5:\"nodes\";a:6:{i:0;a:2:{s:4:\"name\";s:7:\"Default\";s:10:\"attributes\";a:5:{s:6:\"handle\";s:18:\"Product Identifier\";s:4:\"file\";s:14:\"Product Images\";s:6:\"vendor\";s:6:\"Vendor\";s:5:\"brand\";s:5:\"Brand\";s:10:\"categories\";s:8:\"Category\";}}i:1;a:2:{s:4:\"name\";s:7:\"General\";s:10:\"attributes\";a:2:{s:12:\"product_name\";s:12:\"Product Name\";s:11:\"description\";s:11:\"Description\";}}i:2;a:2:{s:4:\"name\";s:7:\"Variant\";s:10:\"attributes\";a:11:{s:3:\"sku\";s:3:\"SKU\";s:4:\"name\";s:4:\"Name\";s:4:\"file\";s:5:\"Image\";s:5:\"price\";s:5:\"Price\";s:16:\"compare_at_price\";s:16:\"Compare at Price\";s:10:\"cost_price\";s:10:\"Cost Price\";s:7:\"barcode\";s:13:\"UPC / Barcode\";s:6:\"weight\";s:6:\"Weight\";s:11:\"weight_unit\";s:11:\"Weight Unit\";s:14:\"track_quantity\";s:14:\"Track Quantity\";s:16:\"continue_selling\";s:16:\"Continue Selling\";}}i:3;a:2:{s:4:\"name\";s:14:\"Variant Option\";s:10:\"attributes\";a:6:{s:12:\"option1_name\";s:13:\"Option 1 Name\";s:13:\"option1_value\";s:14:\"Option 1 Value\";s:12:\"option2_name\";s:13:\"Option 2 Name\";s:13:\"option2_value\";s:14:\"Option 2 Value\";s:12:\"option3_name\";s:13:\"Option 3 Name\";s:13:\"option3_value\";s:14:\"Option 3 Value\";}}i:4;a:2:{s:4:\"name\";s:26:\"Inventory -> Tanzayb Store\";s:10:\"attributes\";a:1:{i:1;s:23:\"Tanzayb Store Warehouse\";}}i:5;a:2:{s:4:\"name\";s:3:\"SEO\";s:10:\"attributes\";a:4:{s:7:\"seo_url\";s:8:\"URL Slug\";s:9:\"seo_title\";s:9:\"SEO Title\";s:15:\"seo_description\";s:15:\"SEO Description\";s:11:\"seo_keyword\";s:4:\"Tags\";}}}}s:22:\"converted_output_array\";a:6:{i:0;a:3:{s:5:\"label\";s:7:\"Default\";s:5:\"title\";s:7:\"Default\";s:7:\"options\";a:5:{i:0;a:2:{s:5:\"label\";s:18:\"Product Identifier\";s:5:\"value\";s:14:\"Default,handle\";}i:1;a:2:{s:5:\"label\";s:14:\"Product Images\";s:5:\"value\";s:12:\"Default,file\";}i:2;a:2:{s:5:\"label\";s:6:\"Vendor\";s:5:\"value\";s:14:\"Default,vendor\";}i:3;a:2:{s:5:\"label\";s:5:\"Brand\";s:5:\"value\";s:13:\"Default,brand\";}i:4;a:2:{s:5:\"label\";s:8:\"Category\";s:5:\"value\";s:18:\"Default,categories\";}}}i:1;a:3:{s:5:\"label\";s:7:\"General\";s:5:\"title\";s:7:\"General\";s:7:\"options\";a:2:{i:0;a:2:{s:5:\"label\";s:12:\"Product Name\";s:5:\"value\";s:20:\"General,product_name\";}i:1;a:2:{s:5:\"label\";s:11:\"Description\";s:5:\"value\";s:19:\"General,description\";}}}i:2;a:3:{s:5:\"label\";s:7:\"Variant\";s:5:\"title\";s:7:\"Variant\";s:7:\"options\";a:11:{i:0;a:2:{s:5:\"label\";s:3:\"SKU\";s:5:\"value\";s:11:\"Variant,sku\";}i:1;a:2:{s:5:\"label\";s:4:\"Name\";s:5:\"value\";s:12:\"Variant,name\";}i:2;a:2:{s:5:\"label\";s:5:\"Image\";s:5:\"value\";s:12:\"Variant,file\";}i:3;a:2:{s:5:\"label\";s:5:\"Price\";s:5:\"value\";s:13:\"Variant,price\";}i:4;a:2:{s:5:\"label\";s:16:\"Compare at Price\";s:5:\"value\";s:24:\"Variant,compare_at_price\";}i:5;a:2:{s:5:\"label\";s:10:\"Cost Price\";s:5:\"value\";s:18:\"Variant,cost_price\";}i:6;a:2:{s:5:\"label\";s:13:\"UPC / Barcode\";s:5:\"value\";s:15:\"Variant,barcode\";}i:7;a:2:{s:5:\"label\";s:6:\"Weight\";s:5:\"value\";s:14:\"Variant,weight\";}i:8;a:2:{s:5:\"label\";s:11:\"Weight Unit\";s:5:\"value\";s:19:\"Variant,weight_unit\";}i:9;a:2:{s:5:\"label\";s:14:\"Track Quantity\";s:5:\"value\";s:22:\"Variant,track_quantity\";}i:10;a:2:{s:5:\"label\";s:16:\"Continue Selling\";s:5:\"value\";s:24:\"Variant,continue_selling\";}}}i:3;a:3:{s:5:\"label\";s:14:\"Variant Option\";s:5:\"title\";s:14:\"Variant Option\";s:7:\"options\";a:6:{i:0;a:2:{s:5:\"label\";s:13:\"Option 1 Name\";s:5:\"value\";s:27:\"Variant Option,option1_name\";}i:1;a:2:{s:5:\"label\";s:14:\"Option 1 Value\";s:5:\"value\";s:28:\"Variant Option,option1_value\";}i:2;a:2:{s:5:\"label\";s:13:\"Option 2 Name\";s:5:\"value\";s:27:\"Variant Option,option2_name\";}i:3;a:2:{s:5:\"label\";s:14:\"Option 2 Value\";s:5:\"value\";s:28:\"Variant Option,option2_value\";}i:4;a:2:{s:5:\"label\";s:13:\"Option 3 Name\";s:5:\"value\";s:27:\"Variant Option,option3_name\";}i:5;a:2:{s:5:\"label\";s:14:\"Option 3 Value\";s:5:\"value\";s:28:\"Variant Option,option3_value\";}}}i:4;a:3:{s:5:\"label\";s:26:\"Inventory -> Tanzayb Store\";s:5:\"title\";s:26:\"Inventory -> Tanzayb Store\";s:7:\"options\";a:1:{i:0;a:2:{s:5:\"label\";s:23:\"Tanzayb Store Warehouse\";s:5:\"value\";s:28:\"Inventory -> Tanzayb Store,1\";}}}i:5;a:3:{s:5:\"label\";s:3:\"SEO\";s:5:\"title\";s:3:\"SEO\";s:7:\"options\";a:4:{i:0;a:2:{s:5:\"label\";s:8:\"URL Slug\";s:5:\"value\";s:11:\"SEO,seo_url\";}i:1;a:2:{s:5:\"label\";s:9:\"SEO Title\";s:5:\"value\";s:13:\"SEO,seo_title\";}i:2;a:2:{s:5:\"label\";s:15:\"SEO Description\";s:5:\"value\";s:19:\"SEO,seo_description\";}i:3;a:2:{s:5:\"label\";s:4:\"Tags\";s:5:\"value\";s:15:\"SEO,seo_keyword\";}}}}s:12:\"mapping_data\";a:4:{i:0;a:3:{s:4:\"from\";a:1:{i:0;s:20:\"Default,Product Name\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:20:\"General,product_name\";}}i:1;a:3:{s:4:\"from\";a:1:{i:0;s:11:\"Default,SKU\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:11:\"Variant,sku\";}}i:2;a:3:{s:4:\"from\";a:1:{i:0;s:13:\"Default,Price\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:1:{i:0;s:13:\"Variant,price\";}}i:3;a:3:{s:4:\"from\";a:1:{i:0;s:16:\"Default,Quantity\";}s:12:\"with_formula\";s:6:\"assign\";s:2:\"to\";a:0:{}}}}}s:4:\"user\";O:8:\"App\\User\":32:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:5:\"users\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:16:{s:2:\"id\";i:1;s:9:\"google_id\";s:21:\"111325706919938389600\";s:15:\"shopify_shop_id\";N;s:13:\"freshworks_id\";N;s:5:\"fname\";s:12:\"Bilal Arshad\";s:5:\"lname\";N;s:5:\"email\";s:23:\"<EMAIL>\";s:2:\"ip\";N;s:17:\"email_verified_at\";s:19:\"2025-06-23 08:21:56\";s:8:\"password\";N;s:5:\"phone\";N;s:10:\"last_login\";s:19:\"2025-06-27 06:52:55\";s:14:\"remember_token\";s:60:\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\";s:12:\"block_status\";i:0;s:10:\"created_at\";s:19:\"2025-06-23 08:21:56\";s:10:\"updated_at\";s:19:\"2025-06-27 06:52:55\";}s:11:\"\u0000*\u0000original\";a:16:{s:2:\"id\";i:1;s:9:\"google_id\";s:21:\"111325706919938389600\";s:15:\"shopify_shop_id\";N;s:13:\"freshworks_id\";N;s:5:\"fname\";s:12:\"Bilal Arshad\";s:5:\"lname\";N;s:5:\"email\";s:23:\"<EMAIL>\";s:2:\"ip\";N;s:17:\"email_verified_at\";s:19:\"2025-06-23 08:21:56\";s:8:\"password\";N;s:5:\"phone\";N;s:10:\"last_login\";s:19:\"2025-06-27 06:52:55\";s:14:\"remember_token\";s:60:\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\";s:12:\"block_status\";i:0;s:10:\"created_at\";s:19:\"2025-06-23 08:21:56\";s:10:\"updated_at\";s:19:\"2025-06-27 06:52:55\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:8:\"\u0000*\u0000casts\";a:1:{s:17:\"email_verified_at\";s:8:\"datetime\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:2:{s:19:\"unreadNotifications\";O:55:\"Illuminate\\Notifications\\DatabaseNotificationCollection\":2:{s:8:\"\u0000*\u0000items\";a:0:{}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}s:13:\"notifications\";O:55:\"Illuminate\\Notifications\\DatabaseNotificationCollection\":2:{s:8:\"\u0000*\u0000items\";a:4:{i:0;O:45:\"Illuminate\\Notifications\\DatabaseNotification\":30:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:13:\"notifications\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:6:\"string\";s:12:\"incrementing\";b:0;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:10:{s:2:\"id\";s:36:\"fe48e0e5-847d-462e-8955-10e4c4cb6209\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:364:\"[{\"subject\":\"Import CSV Products Queue\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your CSV file is now being processed by Apimio. We\\u2019ll notify you once all product data has been successfully imported.\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"View\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\",\"user_id\":1,\"organization_id\":\"1\",\"batch\":null}]\";s:7:\"read_at\";N;s:10:\"created_at\";s:19:\"2025-06-27 07:34:39\";s:10:\"updated_at\";s:19:\"2025-06-27 07:34:39\";}s:11:\"\u0000*\u0000original\";a:10:{s:2:\"id\";s:36:\"fe48e0e5-847d-462e-8955-10e4c4cb6209\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:364:\"[{\"subject\":\"Import CSV Products Queue\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your CSV file is now being processed by Apimio. We\\u2019ll notify you once all product data has been successfully imported.\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"View\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\",\"user_id\":1,\"organization_id\":\"1\",\"batch\":null}]\";s:7:\"read_at\";N;s:10:\"created_at\";s:19:\"2025-06-27 07:34:39\";s:10:\"updated_at\";s:19:\"2025-06-27 07:34:39\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:4:\"data\";s:5:\"array\";s:7:\"read_at\";s:8:\"datetime\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:0:{}}i:1;O:45:\"Illuminate\\Notifications\\DatabaseNotification\":30:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:13:\"notifications\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:6:\"string\";s:12:\"incrementing\";b:0;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:10:{s:2:\"id\";s:36:\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:724:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\",\"user_id\":1,\"organization_id\":\"1\"}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:57\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:11:\"\u0000*\u0000original\";a:10:{s:2:\"id\";s:36:\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:724:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\",\"user_id\":1,\"organization_id\":\"1\"}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:57\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:4:\"data\";s:5:\"array\";s:7:\"read_at\";s:8:\"datetime\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:0:{}}i:2;O:45:\"Illuminate\\Notifications\\DatabaseNotification\":30:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:13:\"notifications\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:6:\"string\";s:12:\"incrementing\";b:0;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:10:{s:2:\"id\";s:36:\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:722:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\",\"user_id\":1,\"organization_id\":1}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:54\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:11:\"\u0000*\u0000original\";a:10:{s:2:\"id\";s:36:\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:722:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\",\"user_id\":1,\"organization_id\":1}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:54\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:4:\"data\";s:5:\"array\";s:7:\"read_at\";s:8:\"datetime\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:0:{}}i:3;O:45:\"Illuminate\\Notifications\\DatabaseNotification\":30:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:13:\"notifications\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:6:\"string\";s:12:\"incrementing\";b:0;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:10:{s:2:\"id\";s:36:\"2db6769b-b1d5-400c-974f-244e7b85d1db\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:722:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\",\"user_id\":1,\"organization_id\":1}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:52\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:11:\"\u0000*\u0000original\";a:10:{s:2:\"id\";s:36:\"2db6769b-b1d5-400c-974f-244e7b85d1db\";s:15:\"organization_id\";i:1;s:7:\"user_id\";i:1;s:4:\"type\";s:36:\"App\\Notifications\\ApimioNotification\";s:15:\"notifiable_type\";s:8:\"App\\User\";s:13:\"notifiable_id\";i:1;s:4:\"data\";s:722:\"[{\"subject\":\"Your export CSV file is ready to download\",\"greeting\":\"Hi Bilal Arshad\",\"body\":\"Your ( custom ) Export CSV with version <b>EN-US<\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx'>http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\/a><br>\",\"thanks\":\"Thank you for using localhost:8000\",\"actionText\":\"Download CSV File\",\"actionURL\":\"http:\\/\\/localhost:8000\\/products\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\",\"user_id\":1,\"organization_id\":1}]\";s:7:\"read_at\";s:19:\"2025-06-27 07:33:08\";s:10:\"created_at\";s:19:\"2025-06-26 11:09:52\";s:10:\"updated_at\";s:19:\"2025-06-27 07:33:08\";}s:10:\"\u0000*\u0000changes\";a:0:{}s:8:\"\u0000*\u0000casts\";a:2:{s:4:\"data\";s:5:\"array\";s:7:\"read_at\";s:8:\"datetime\";}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:0:{}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}}s:10:\"\u0000*\u0000touches\";a:0:{}s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:2:{i:0;s:8:\"password\";i:1;s:14:\"remember_token\";}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:9:{i:0;s:5:\"fname\";i:1;s:5:\"lname\";i:2;s:5:\"email\";i:3;s:8:\"password\";i:4;s:17:\"verification_code\";i:5;s:5:\"phone\";i:6;s:17:\"email_verified_at\";i:7;s:15:\"shopify_shop_id\";i:8;s:10:\"last_login\";}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}s:20:\"\u0000*\u0000rememberTokenName\";s:14:\"remember_token\";s:14:\"\u0000*\u0000accessToken\";N;}s:15:\"organization_id\";s:1:\"1\";s:8:\"filename\";s:48:\"temp_files/error_products_2025_06_27_073426.xlsx\";s:5:\"batch\";N;s:15:\"notification_id\";s:36:\"fe48e0e5-847d-462e-8955-10e4c4cb6209\";}}s:8:\"function\";s:172:\"function (\\Illuminate\\Bus\\Batch $batch) use ($data) {\r\n                        \\Illuminate\\Support\\Facades\\Log::channel('mapping')->info(\"job done\");\r\n                    }\";s:5:\"scope\";s:45:\"App\\Http\\Controllers\\Product\\ImportController\";s:4:\"this\";N;s:4:\"self\";s:32:\"0000000000000bf10000000000000000\";}\";s:4:\"hash\";s:44:\"6YRyYZ0gejBiXV/3oIS4mZ4N6Tb3KdYXx9d50H0vxe8=\";}}}}", **********, null, null], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 99}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 403}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 310}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 772}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.798631, "duration": 0.009789999999999998, "duration_str": "9.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:99", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=99", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "99"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.175, "width_percent": 20.507}, {"sql": "select * from `job_batches` where `id` = '9f40defe-f107-453e-9f00-fc7223b0ae3c' limit 1", "type": "query", "params": [], "bindings": ["9f40defe-f107-453e-9f00-fc7223b0ae3c"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 82}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 112}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 403}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 310}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 772}], "start": **********.812921, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:82", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=82", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "82"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.682, "width_percent": 4.504}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 186}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 312}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 772}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.87715, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:312", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=312", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "312"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.186, "width_percent": 0}, {"sql": "update `job_batches` set `total_jobs` = total_jobs + 0, `pending_jobs` = pending_jobs + 0, `finished_at` = null where `id` = '9f40defe-f107-453e-9f00-fc7223b0ae3c'", "type": "query", "params": [], "bindings": [null, "9f40defe-f107-453e-9f00-fc7223b0ae3c"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 124}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 187}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 186}], "start": **********.8775392, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:124", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=124", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.186, "width_percent": 1.299}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 186}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 312}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 772}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.887441, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:312", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 312}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=312", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "312"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.484, "width_percent": 0}, {"sql": "select * from `job_batches` where `id` = '9f40defe-f107-453e-9f00-fc7223b0ae3c' limit 1", "type": "query", "params": [], "bindings": ["9f40defe-f107-453e-9f00-fc7223b0ae3c"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 82}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 152}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Batch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Batch.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/PendingBatch.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\PendingBatch.php", "line": 312}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 772}], "start": **********.887787, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseBatchRepository.php:82", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/DatabaseBatchRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\DatabaseBatchRepository.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FDatabaseBatchRepository.php&line=82", "ajax": false, "filename": "DatabaseBatchRepository.php", "line": "82"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.484, "width_percent": 1.487}, {"sql": "insert into `batch_progress` (`organization_id`, `user_id`, `batch_id`, `type`, `updated_at`, `created_at`) values ('1', 1, '9f40defe-f107-453e-9f00-fc7223b0ae3c', 'import_csv', '2025-06-27 07:34:39', '2025-06-27 07:34:39')", "type": "query", "params": [], "bindings": ["1", 1, "9f40defe-f107-453e-9f00-fc7223b0ae3c", "import_csv", "2025-06-27 07:34:39", "2025-06-27 07:34:39"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/BatchProgress.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\BatchProgress.php", "line": 53}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 782}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9119709, "duration": 0.008320000000000001, "duration_str": "8.32ms", "memory": 0, "memory_str": null, "filename": "BatchProgress.php:53", "source": {"index": 15, "namespace": null, "name": "app/Models/BatchProgress.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\BatchProgress.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FBatchProgress.php&line=53", "ajax": false, "filename": "BatchProgress.php", "line": "53"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 63.972, "width_percent": 17.428}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"9e5e5ddb-09a8-45fb-901d-eaed6457cac2\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\ImportProducts\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\ImportProducts\\\",\\\"command\\\":\\\"O:23:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\ImportProducts\\\\\\\":1:{s:12:\\\\\\\"request_data\\\\\\\";a:6:{s:8:\\\\\\\"all_data\\\\\\\";a:15:{s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:20:\\\\\\\"template_method_type\\\\\\\";s:6:\\\\\\\"import\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:1:{s:4:\\\\\\\"data\\\\\\\";a:4:{i:0;a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:20:\\\\\\\"Default,Product Name\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Default,handle\\\\\\\";}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"2249526f-665d-4bbf-9125-65742334829b\\\\\\\";}i:1;a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Default,SKU\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Variant,sku\\\\\\\";}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"694a4d9c-2ead-41c6-accb-e20750e9e972\\\\\\\";}i:2;a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Default,Price\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Variant,price\\\\\\\";}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"c381d306-5a4d-4a40-bc3a-cee8403559e3\\\\\\\";}i:3;a:4:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:16:\\\\\\\"Default,Quantity\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:18:\\\\\\\"Default,categories\\\\\\\";}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"7d3dcbfb-172b-498b-a89d-c1a632ce2c3c\\\\\\\";}}}s:7:\\\\\\\"version\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:7:\\\\\\\"catalog\\\\\\\";a:1:{i:0;s:1:\\\\\\\"1\\\\\\\";}s:6:\\\\\\\"status\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:11:\\\\\\\"temp_status\\\\\\\";s:3:\\\\\\\"off\\\\\\\";s:9:\\\\\\\"temp_name\\\\\\\";s:14:\\\\\\\"Apimio Default\\\\\\\";s:7:\\\\\\\"temp_id\\\\\\\";N;s:11:\\\\\\\"export_type\\\\\\\";N;s:13:\\\\\\\"import_action\\\\\\\";s:1:\\\\\\\"3\\\\\\\";s:9:\\\\\\\"file_path\\\\\\\";s:50:\\\\\\\"mapping_fields\\\\/upload\\\\/json\\\\/1751009623_datafile.csv\\\\\\\";s:15:\\\\\\\"ignore_unmapped\\\\\\\";s:3:\\\\\\\"off\\\\\\\";s:16:\\\\\\\"all_product_skus\\\\\\\";a:1:{s:15:\\\\\\\"testing-product\\\\\\\";a:0:{}}s:12:\\\\\\\"request_data\\\\\\\";a:11:{s:11:\\\\\\\"input_array\\\\\\\";a:2:{s:10:\\\\\\\"array_name\\\\\\\";s:3:\\\\\\\"CSV\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:1:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:4:{s:12:\\\\\\\"Product Name\\\\\\\";s:12:\\\\\\\"Product Name\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:8:\\\\\\\"Quantity\\\\\\\";s:8:\\\\\\\"Quantity\\\\\\\";}}}}s:21:\\\\\\\"converted_input_array\\\\\\\";a:1:{i:0;a:3:{s:5:\\\\\\\"label\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:4:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:12:\\\\\\\"Product Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:20:\\\\\\\"Default,Product Name\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:11:\\\\\\\"Default,SKU\\\\\\\";}i:2;a:2:{s:5:\\\\\\\"label\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:13:\\\\\\\"Default,Price\\\\\\\";}i:3;a:2:{s:5:\\\\\\\"label\\\\\\\";s:8:\\\\\\\"Quantity\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:16:\\\\\\\"Default,Quantity\\\\\\\";}}}}s:9:\\\\\\\"file_path\\\\\\\";s:50:\\\\\\\"mapping_fields\\\\/upload\\\\/json\\\\/1751009623_datafile.csv\\\\\\\";s:8:\\\\\\\"file_url\\\\\\\";s:100:\\\\\\\"https:\\\\/\\\\/apimio-staging.s3.us-east-2.amazonaws.com\\\\/mapping_fields\\\\/upload\\\\/json\\\\/1751009623_datafile.csv\\\\\\\";s:13:\\\\\\\"data_required\\\\\\\";a:8:{s:20:\\\\\\\"template_method_type\\\\\\\";s:6:\\\\\\\"import\\\\\\\";s:11:\\\\\\\"output_type\\\\\\\";s:6:\\\\\\\"Apimio\\\\\\\";s:4:\\\\\\\"sync\\\\\\\";b:0;s:18:\\\\\\\"redirect_url_route\\\\\\\";s:15:\\\\\\\"products.import\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:8:\\\\\\\"versions\\\\\\\";a:1:{i:1;s:5:\\\\\\\"EN-US\\\\\\\";}s:8:\\\\\\\"catalogs\\\\\\\";a:1:{i:1;s:13:\\\\\\\"Tanzayb Store\\\\\\\";}s:13:\\\\\\\"import_action\\\\\\\";s:1:\\\\\\\"3\\\\\\\";}s:13:\\\\\\\"import_action\\\\\\\";s:1:\\\\\\\"3\\\\\\\";s:26:\\\\\\\"apimio_attributes_required\\\\\\\";a:2:{s:12:\\\\\\\"all_families\\\\\\\";a:0:{}s:14:\\\\\\\"all_attributes\\\\\\\";a:6:{i:0;a:2:{s:2:\\\\\\\"id\\\\\\\";i:1;s:4:\\\\\\\"name\\\\\\\";s:16:\\\\\\\"single line text\\\\\\\";}i:1;a:2:{s:2:\\\\\\\"id\\\\\\\";i:2;s:4:\\\\\\\"name\\\\\\\";s:6:\\\\\\\"number\\\\\\\";}i:2;a:2:{s:2:\\\\\\\"id\\\\\\\";i:3;s:4:\\\\\\\"name\\\\\\\";s:15:\\\\\\\"multi line text\\\\\\\";}i:3;a:2:{s:2:\\\\\\\"id\\\\\\\";i:7;s:4:\\\\\\\"name\\\\\\\";s:11:\\\\\\\"measurement\\\\\\\";}i:4;a:2:{s:2:\\\\\\\"id\\\\\\\";i:9;s:4:\\\\\\\"name\\\\\\\";s:4:\\\\\\\"json\\\\\\\";}i:5;a:2:{s:2:\\\\\\\"id\\\\\\\";i:11;s:4:\\\\\\\"name\\\\\\\";s:3:\\\\\\\"url\\\\\\\";}}}s:19:\\\\\\\"template_attributes\\\\\\\";a:0:{}s:12:\\\\\\\"output_array\\\\\\\";a:2:{s:10:\\\\\\\"array_name\\\\\\\";s:6:\\\\\\\"Apimio\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:6:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:5:{s:6:\\\\\\\"handle\\\\\\\";s:18:\\\\\\\"Product Identifier\\\\\\\";s:4:\\\\\\\"file\\\\\\\";s:14:\\\\\\\"Product Images\\\\\\\";s:6:\\\\\\\"vendor\\\\\\\";s:6:\\\\\\\"Vendor\\\\\\\";s:5:\\\\\\\"brand\\\\\\\";s:5:\\\\\\\"Brand\\\\\\\";s:10:\\\\\\\"categories\\\\\\\";s:8:\\\\\\\"Category\\\\\\\";}}i:1;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"General\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:2:{s:12:\\\\\\\"product_name\\\\\\\";s:12:\\\\\\\"Product Name\\\\\\\";s:11:\\\\\\\"description\\\\\\\";s:11:\\\\\\\"Description\\\\\\\";}}i:2;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"Variant\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:11:{s:3:\\\\\\\"sku\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:4:\\\\\\\"name\\\\\\\";s:4:\\\\\\\"Name\\\\\\\";s:4:\\\\\\\"file\\\\\\\";s:5:\\\\\\\"Image\\\\\\\";s:5:\\\\\\\"price\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:16:\\\\\\\"compare_at_price\\\\\\\";s:16:\\\\\\\"Compare at Price\\\\\\\";s:10:\\\\\\\"cost_price\\\\\\\";s:10:\\\\\\\"Cost Price\\\\\\\";s:7:\\\\\\\"barcode\\\\\\\";s:13:\\\\\\\"UPC \\\\/ Barcode\\\\\\\";s:6:\\\\\\\"weight\\\\\\\";s:6:\\\\\\\"Weight\\\\\\\";s:11:\\\\\\\"weight_unit\\\\\\\";s:11:\\\\\\\"Weight Unit\\\\\\\";s:14:\\\\\\\"track_quantity\\\\\\\";s:14:\\\\\\\"Track Quantity\\\\\\\";s:16:\\\\\\\"continue_selling\\\\\\\";s:16:\\\\\\\"Continue Selling\\\\\\\";}}i:3;a:2:{s:4:\\\\\\\"name\\\\\\\";s:14:\\\\\\\"Variant Option\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:6:{s:12:\\\\\\\"option1_name\\\\\\\";s:13:\\\\\\\"Option 1 Name\\\\\\\";s:13:\\\\\\\"option1_value\\\\\\\";s:14:\\\\\\\"Option 1 Value\\\\\\\";s:12:\\\\\\\"option2_name\\\\\\\";s:13:\\\\\\\"Option 2 Name\\\\\\\";s:13:\\\\\\\"option2_value\\\\\\\";s:14:\\\\\\\"Option 2 Value\\\\\\\";s:12:\\\\\\\"option3_name\\\\\\\";s:13:\\\\\\\"Option 3 Name\\\\\\\";s:13:\\\\\\\"option3_value\\\\\\\";s:14:\\\\\\\"Option 3 Value\\\\\\\";}}i:4;a:2:{s:4:\\\\\\\"name\\\\\\\";s:26:\\\\\\\"Inventory -> Tanzayb Store\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:1:{i:1;s:23:\\\\\\\"Tanzayb Store Warehouse\\\\\\\";}}i:5;a:2:{s:4:\\\\\\\"name\\\\\\\";s:3:\\\\\\\"SEO\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:4:{s:7:\\\\\\\"seo_url\\\\\\\";s:8:\\\\\\\"URL Slug\\\\\\\";s:9:\\\\\\\"seo_title\\\\\\\";s:9:\\\\\\\"SEO Title\\\\\\\";s:15:\\\\\\\"seo_description\\\\\\\";s:15:\\\\\\\"SEO Description\\\\\\\";s:11:\\\\\\\"seo_keyword\\\\\\\";s:4:\\\\\\\"Tags\\\\\\\";}}}}s:22:\\\\\\\"converted_output_array\\\\\\\";a:6:{i:0;a:3:{s:5:\\\\\\\"label\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:5:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:18:\\\\\\\"Product Identifier\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:14:\\\\\\\"Default,handle\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Product Images\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:12:\\\\\\\"Default,file\\\\\\\";}i:2;a:2:{s:5:\\\\\\\"label\\\\\\\";s:6:\\\\\\\"Vendor\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:14:\\\\\\\"Default,vendor\\\\\\\";}i:3;a:2:{s:5:\\\\\\\"label\\\\\\\";s:5:\\\\\\\"Brand\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:13:\\\\\\\"Default,brand\\\\\\\";}i:4;a:2:{s:5:\\\\\\\"label\\\\\\\";s:8:\\\\\\\"Category\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:18:\\\\\\\"Default,categories\\\\\\\";}}}i:1;a:3:{s:5:\\\\\\\"label\\\\\\\";s:7:\\\\\\\"General\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:7:\\\\\\\"General\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:2:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:12:\\\\\\\"Product Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:20:\\\\\\\"General,product_name\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:11:\\\\\\\"Description\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:19:\\\\\\\"General,description\\\\\\\";}}}i:2;a:3:{s:5:\\\\\\\"label\\\\\\\";s:7:\\\\\\\"Variant\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:7:\\\\\\\"Variant\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:11:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:11:\\\\\\\"Variant,sku\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:4:\\\\\\\"Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:12:\\\\\\\"Variant,name\\\\\\\";}i:2;a:2:{s:5:\\\\\\\"label\\\\\\\";s:5:\\\\\\\"Image\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:12:\\\\\\\"Variant,file\\\\\\\";}i:3;a:2:{s:5:\\\\\\\"label\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:13:\\\\\\\"Variant,price\\\\\\\";}i:4;a:2:{s:5:\\\\\\\"label\\\\\\\";s:16:\\\\\\\"Compare at Price\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:24:\\\\\\\"Variant,compare_at_price\\\\\\\";}i:5;a:2:{s:5:\\\\\\\"label\\\\\\\";s:10:\\\\\\\"Cost Price\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:18:\\\\\\\"Variant,cost_price\\\\\\\";}i:6;a:2:{s:5:\\\\\\\"label\\\\\\\";s:13:\\\\\\\"UPC \\\\/ Barcode\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:15:\\\\\\\"Variant,barcode\\\\\\\";}i:7;a:2:{s:5:\\\\\\\"label\\\\\\\";s:6:\\\\\\\"Weight\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:14:\\\\\\\"Variant,weight\\\\\\\";}i:8;a:2:{s:5:\\\\\\\"label\\\\\\\";s:11:\\\\\\\"Weight Unit\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:19:\\\\\\\"Variant,weight_unit\\\\\\\";}i:9;a:2:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Track Quantity\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:22:\\\\\\\"Variant,track_quantity\\\\\\\";}i:10;a:2:{s:5:\\\\\\\"label\\\\\\\";s:16:\\\\\\\"Continue Selling\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:24:\\\\\\\"Variant,continue_selling\\\\\\\";}}}i:3;a:3:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Variant Option\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:14:\\\\\\\"Variant Option\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:6:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:13:\\\\\\\"Option 1 Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:27:\\\\\\\"Variant Option,option1_name\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Option 1 Value\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:28:\\\\\\\"Variant Option,option1_value\\\\\\\";}i:2;a:2:{s:5:\\\\\\\"label\\\\\\\";s:13:\\\\\\\"Option 2 Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:27:\\\\\\\"Variant Option,option2_name\\\\\\\";}i:3;a:2:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Option 2 Value\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:28:\\\\\\\"Variant Option,option2_value\\\\\\\";}i:4;a:2:{s:5:\\\\\\\"label\\\\\\\";s:13:\\\\\\\"Option 3 Name\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:27:\\\\\\\"Variant Option,option3_name\\\\\\\";}i:5;a:2:{s:5:\\\\\\\"label\\\\\\\";s:14:\\\\\\\"Option 3 Value\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:28:\\\\\\\"Variant Option,option3_value\\\\\\\";}}}i:4;a:3:{s:5:\\\\\\\"label\\\\\\\";s:26:\\\\\\\"Inventory -> Tanzayb Store\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:26:\\\\\\\"Inventory -> Tanzayb Store\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:1:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:23:\\\\\\\"Tanzayb Store Warehouse\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:28:\\\\\\\"Inventory -> Tanzayb Store,1\\\\\\\";}}}i:5;a:3:{s:5:\\\\\\\"label\\\\\\\";s:3:\\\\\\\"SEO\\\\\\\";s:5:\\\\\\\"title\\\\\\\";s:3:\\\\\\\"SEO\\\\\\\";s:7:\\\\\\\"options\\\\\\\";a:4:{i:0;a:2:{s:5:\\\\\\\"label\\\\\\\";s:8:\\\\\\\"URL Slug\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:11:\\\\\\\"SEO,seo_url\\\\\\\";}i:1;a:2:{s:5:\\\\\\\"label\\\\\\\";s:9:\\\\\\\"SEO Title\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:13:\\\\\\\"SEO,seo_title\\\\\\\";}i:2;a:2:{s:5:\\\\\\\"label\\\\\\\";s:15:\\\\\\\"SEO Description\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:19:\\\\\\\"SEO,seo_description\\\\\\\";}i:3;a:2:{s:5:\\\\\\\"label\\\\\\\";s:4:\\\\\\\"Tags\\\\\\\";s:5:\\\\\\\"value\\\\\\\";s:15:\\\\\\\"SEO,seo_keyword\\\\\\\";}}}}s:12:\\\\\\\"mapping_data\\\\\\\";a:4:{i:0;a:3:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:20:\\\\\\\"Default,Product Name\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:20:\\\\\\\"General,product_name\\\\\\\";}}i:1;a:3:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Default,SKU\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Variant,sku\\\\\\\";}}i:2;a:3:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Default,Price\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Variant,price\\\\\\\";}}i:3;a:3:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:16:\\\\\\\"Default,Quantity\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:0:{}}}}}s:4:\\\\\\\"user\\\\\\\";O:8:\\\\\\\"App\\\\\\\\User\\\\\\\":32:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:5:\\\\\\\"users\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:3:\\\\\\\"int\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:1;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:16:{s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"google_id\\\\\\\";s:21:\\\\\\\"111325706919938389600\\\\\\\";s:15:\\\\\\\"shopify_shop_id\\\\\\\";N;s:13:\\\\\\\"freshworks_id\\\\\\\";N;s:5:\\\\\\\"fname\\\\\\\";s:12:\\\\\\\"Bilal Arshad\\\\\\\";s:5:\\\\\\\"lname\\\\\\\";N;s:5:\\\\\\\"email\\\\\\\";s:23:\\\\\\\"<EMAIL>\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";N;s:17:\\\\\\\"email_verified_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:8:\\\\\\\"password\\\\\\\";N;s:5:\\\\\\\"phone\\\\\\\";N;s:10:\\\\\\\"last_login\\\\\\\";s:19:\\\\\\\"2025-06-27 06:52:55\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:60:\\\\\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\\\\\";s:12:\\\\\\\"block_status\\\\\\\";i:0;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 06:52:55\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:16:{s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"google_id\\\\\\\";s:21:\\\\\\\"111325706919938389600\\\\\\\";s:15:\\\\\\\"shopify_shop_id\\\\\\\";N;s:13:\\\\\\\"freshworks_id\\\\\\\";N;s:5:\\\\\\\"fname\\\\\\\";s:12:\\\\\\\"Bilal Arshad\\\\\\\";s:5:\\\\\\\"lname\\\\\\\";N;s:5:\\\\\\\"email\\\\\\\";s:23:\\\\\\\"<EMAIL>\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";N;s:17:\\\\\\\"email_verified_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:8:\\\\\\\"password\\\\\\\";N;s:5:\\\\\\\"phone\\\\\\\";N;s:10:\\\\\\\"last_login\\\\\\\";s:19:\\\\\\\"2025-06-27 06:52:55\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:60:\\\\\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\\\\\";s:12:\\\\\\\"block_status\\\\\\\";i:0;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 06:52:55\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:1:{s:17:\\\\\\\"email_verified_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:2:{s:19:\\\\\\\"unreadNotifications\\\\\\\";O:55:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotificationCollection\\\\\\\":2:{s:8:\\\\\\\"\\\\u0000*\\\\u0000items\\\\\\\";a:0:{}s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;}s:13:\\\\\\\"notifications\\\\\\\";O:55:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotificationCollection\\\\\\\":2:{s:8:\\\\\\\"\\\\u0000*\\\\u0000items\\\\\\\";a:4:{i:0;O:45:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotification\\\\\\\":30:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:13:\\\\\\\"notifications\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:6:\\\\\\\"string\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:0;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:364:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Import CSV Products Queue\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your CSV file is now being processed by Apimio. We\\\\\\\\u2019ll notify you once all product data has been successfully imported.\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"View\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"batch\\\\\\\":null}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";N;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:34:39\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:34:39\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:364:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Import CSV Products Queue\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your CSV file is now being processed by Apimio. We\\\\\\\\u2019ll notify you once all product data has been successfully imported.\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"View\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"batch\\\\\\\":null}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";N;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:34:39\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:34:39\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:2:{s:4:\\\\\\\"data\\\\\\\";s:5:\\\\\\\"array\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:0:{}}i:1;O:45:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotification\\\\\\\":30:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:13:\\\\\\\"notifications\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:6:\\\\\\\"string\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:0;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:724:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":\\\\\\\"1\\\\\\\"}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:57\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:724:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":\\\\\\\"1\\\\\\\"}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:57\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:2:{s:4:\\\\\\\"data\\\\\\\";s:5:\\\\\\\"array\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:0:{}}i:2;O:45:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotification\\\\\\\":30:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:13:\\\\\\\"notifications\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:6:\\\\\\\"string\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:0;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:722:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":1}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:54\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:722:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":1}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:54\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:2:{s:4:\\\\\\\"data\\\\\\\";s:5:\\\\\\\"array\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:0:{}}i:3;O:45:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\DatabaseNotification\\\\\\\":30:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:13:\\\\\\\"notifications\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:6:\\\\\\\"string\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:0;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:722:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":1}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:52\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:10:{s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\\\\\";s:15:\\\\\\\"organization_id\\\\\\\";i:1;s:7:\\\\\\\"user_id\\\\\\\";i:1;s:4:\\\\\\\"type\\\\\\\";s:36:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\ApimioNotification\\\\\\\";s:15:\\\\\\\"notifiable_type\\\\\\\";s:8:\\\\\\\"App\\\\\\\\User\\\\\\\";s:13:\\\\\\\"notifiable_id\\\\\\\";i:1;s:4:\\\\\\\"data\\\\\\\";s:722:\\\\\\\"[{\\\\\\\"subject\\\\\\\":\\\\\\\"Your export CSV file is ready to download\\\\\\\",\\\\\\\"greeting\\\\\\\":\\\\\\\"Hi Bilal Arshad\\\\\\\",\\\\\\\"body\\\\\\\":\\\\\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href=\\'http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\'>http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\\\\\\\\\\\/a><br>\\\\\\\",\\\\\\\"thanks\\\\\\\":\\\\\\\"Thank you for using localhost:8000\\\\\\\",\\\\\\\"actionText\\\\\\\":\\\\\\\"Download CSV File\\\\\\\",\\\\\\\"actionURL\\\\\\\":\\\\\\\"http:\\\\\\\\\\\\/\\\\\\\\\\\\/localhost:8000\\\\\\\\\\\\/products\\\\\\\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\\\\\",\\\\\\\"user_id\\\\\\\":1,\\\\\\\"organization_id\\\\\\\":1}]\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-26 11:09:52\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-27 07:33:08\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:2:{s:4:\\\\\\\"data\\\\\\\";s:5:\\\\\\\"array\\\\\\\";s:7:\\\\\\\"read_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:0:{}}}s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;}}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:2:{i:0;s:8:\\\\\\\"password\\\\\\\";i:1;s:14:\\\\\\\"remember_token\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:9:{i:0;s:5:\\\\\\\"fname\\\\\\\";i:1;s:5:\\\\\\\"lname\\\\\\\";i:2;s:5:\\\\\\\"email\\\\\\\";i:3;s:8:\\\\\\\"password\\\\\\\";i:4;s:17:\\\\\\\"verification_code\\\\\\\";i:5;s:5:\\\\\\\"phone\\\\\\\";i:6;s:17:\\\\\\\"email_verified_at\\\\\\\";i:7;s:15:\\\\\\\"shopify_shop_id\\\\\\\";i:8;s:10:\\\\\\\"last_login\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:1:{i:0;s:1:\\\\\\\"*\\\\\\\";}s:20:\\\\\\\"\\\\u0000*\\\\u0000rememberTokenName\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:14:\\\\\\\"\\\\u0000*\\\\u0000accessToken\\\\\\\";N;}s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:8:\\\\\\\"filename\\\\\\\";s:48:\\\\\\\"temp_files\\\\/error_products_2025_06_27_073426.xlsx\\\\\\\";s:5:\\\\\\\"batch\\\\\\\";s:36:\\\\\\\"9f40defe-f107-453e-9f00-fc7223b0ae3c\\\\\\\";s:15:\\\\\\\"notification_id\\\\\\\";s:36:\\\\\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"9e5e5ddb-09a8-45fb-901d-eaed6457cac2\",\"displayName\":\"App\\\\Jobs\\\\ImportProducts\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ImportProducts\",\"command\":\"O:23:\\\"App\\\\Jobs\\\\ImportProducts\\\":1:{s:12:\\\"request_data\\\";a:6:{s:8:\\\"all_data\\\";a:15:{s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:20:\\\"template_method_type\\\";s:6:\\\"import\\\";s:5:\\\"nodes\\\";a:1:{s:4:\\\"data\\\";a:4:{i:0;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:20:\\\"Default,Product Name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:14:\\\"Default,handle\\\";}s:2:\\\"id\\\";s:36:\\\"2249526f-665d-4bbf-9125-65742334829b\\\";}i:1;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"Default,SKU\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Variant,sku\\\";}s:2:\\\"id\\\";s:36:\\\"694a4d9c-2ead-41c6-accb-e20750e9e972\\\";}i:2;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Default,Price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:13:\\\"Variant,price\\\";}s:2:\\\"id\\\";s:36:\\\"c381d306-5a4d-4a40-bc3a-cee8403559e3\\\";}i:3;a:4:{s:4:\\\"from\\\";a:1:{i:0;s:16:\\\"Default,Quantity\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:18:\\\"Default,categories\\\";}s:2:\\\"id\\\";s:36:\\\"7d3dcbfb-172b-498b-a89d-c1a632ce2c3c\\\";}}}s:7:\\\"version\\\";s:1:\\\"1\\\";s:7:\\\"catalog\\\";a:1:{i:0;s:1:\\\"1\\\";}s:6:\\\"status\\\";s:1:\\\"1\\\";s:11:\\\"temp_status\\\";s:3:\\\"off\\\";s:9:\\\"temp_name\\\";s:14:\\\"Apimio Default\\\";s:7:\\\"temp_id\\\";N;s:11:\\\"export_type\\\";N;s:13:\\\"import_action\\\";s:1:\\\"3\\\";s:9:\\\"file_path\\\";s:50:\\\"mapping_fields\\/upload\\/json\\/1751009623_datafile.csv\\\";s:15:\\\"ignore_unmapped\\\";s:3:\\\"off\\\";s:16:\\\"all_product_skus\\\";a:1:{s:15:\\\"testing-product\\\";a:0:{}}s:12:\\\"request_data\\\";a:11:{s:11:\\\"input_array\\\";a:2:{s:10:\\\"array_name\\\";s:3:\\\"CSV\\\";s:5:\\\"nodes\\\";a:1:{i:0;a:2:{s:4:\\\"name\\\";s:7:\\\"Default\\\";s:10:\\\"attributes\\\";a:4:{s:12:\\\"Product Name\\\";s:12:\\\"Product Name\\\";s:3:\\\"SKU\\\";s:3:\\\"SKU\\\";s:5:\\\"Price\\\";s:5:\\\"Price\\\";s:8:\\\"Quantity\\\";s:8:\\\"Quantity\\\";}}}}s:21:\\\"converted_input_array\\\";a:1:{i:0;a:3:{s:5:\\\"label\\\";s:7:\\\"Default\\\";s:5:\\\"title\\\";s:7:\\\"Default\\\";s:7:\\\"options\\\";a:4:{i:0;a:2:{s:5:\\\"label\\\";s:12:\\\"Product Name\\\";s:5:\\\"value\\\";s:20:\\\"Default,Product Name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:3:\\\"SKU\\\";s:5:\\\"value\\\";s:11:\\\"Default,SKU\\\";}i:2;a:2:{s:5:\\\"label\\\";s:5:\\\"Price\\\";s:5:\\\"value\\\";s:13:\\\"Default,Price\\\";}i:3;a:2:{s:5:\\\"label\\\";s:8:\\\"Quantity\\\";s:5:\\\"value\\\";s:16:\\\"Default,Quantity\\\";}}}}s:9:\\\"file_path\\\";s:50:\\\"mapping_fields\\/upload\\/json\\/1751009623_datafile.csv\\\";s:8:\\\"file_url\\\";s:100:\\\"https:\\/\\/apimio-staging.s3.us-east-2.amazonaws.com\\/mapping_fields\\/upload\\/json\\/1751009623_datafile.csv\\\";s:13:\\\"data_required\\\";a:8:{s:20:\\\"template_method_type\\\";s:6:\\\"import\\\";s:11:\\\"output_type\\\";s:6:\\\"Apimio\\\";s:4:\\\"sync\\\";b:0;s:18:\\\"redirect_url_route\\\";s:15:\\\"products.import\\\";s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:8:\\\"versions\\\";a:1:{i:1;s:5:\\\"EN-US\\\";}s:8:\\\"catalogs\\\";a:1:{i:1;s:13:\\\"Tanzayb Store\\\";}s:13:\\\"import_action\\\";s:1:\\\"3\\\";}s:13:\\\"import_action\\\";s:1:\\\"3\\\";s:26:\\\"apimio_attributes_required\\\";a:2:{s:12:\\\"all_families\\\";a:0:{}s:14:\\\"all_attributes\\\";a:6:{i:0;a:2:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:16:\\\"single line text\\\";}i:1;a:2:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:6:\\\"number\\\";}i:2;a:2:{s:2:\\\"id\\\";i:3;s:4:\\\"name\\\";s:15:\\\"multi line text\\\";}i:3;a:2:{s:2:\\\"id\\\";i:7;s:4:\\\"name\\\";s:11:\\\"measurement\\\";}i:4;a:2:{s:2:\\\"id\\\";i:9;s:4:\\\"name\\\";s:4:\\\"json\\\";}i:5;a:2:{s:2:\\\"id\\\";i:11;s:4:\\\"name\\\";s:3:\\\"url\\\";}}}s:19:\\\"template_attributes\\\";a:0:{}s:12:\\\"output_array\\\";a:2:{s:10:\\\"array_name\\\";s:6:\\\"Apimio\\\";s:5:\\\"nodes\\\";a:6:{i:0;a:2:{s:4:\\\"name\\\";s:7:\\\"Default\\\";s:10:\\\"attributes\\\";a:5:{s:6:\\\"handle\\\";s:18:\\\"Product Identifier\\\";s:4:\\\"file\\\";s:14:\\\"Product Images\\\";s:6:\\\"vendor\\\";s:6:\\\"Vendor\\\";s:5:\\\"brand\\\";s:5:\\\"Brand\\\";s:10:\\\"categories\\\";s:8:\\\"Category\\\";}}i:1;a:2:{s:4:\\\"name\\\";s:7:\\\"General\\\";s:10:\\\"attributes\\\";a:2:{s:12:\\\"product_name\\\";s:12:\\\"Product Name\\\";s:11:\\\"description\\\";s:11:\\\"Description\\\";}}i:2;a:2:{s:4:\\\"name\\\";s:7:\\\"Variant\\\";s:10:\\\"attributes\\\";a:11:{s:3:\\\"sku\\\";s:3:\\\"SKU\\\";s:4:\\\"name\\\";s:4:\\\"Name\\\";s:4:\\\"file\\\";s:5:\\\"Image\\\";s:5:\\\"price\\\";s:5:\\\"Price\\\";s:16:\\\"compare_at_price\\\";s:16:\\\"Compare at Price\\\";s:10:\\\"cost_price\\\";s:10:\\\"Cost Price\\\";s:7:\\\"barcode\\\";s:13:\\\"UPC \\/ Barcode\\\";s:6:\\\"weight\\\";s:6:\\\"Weight\\\";s:11:\\\"weight_unit\\\";s:11:\\\"Weight Unit\\\";s:14:\\\"track_quantity\\\";s:14:\\\"Track Quantity\\\";s:16:\\\"continue_selling\\\";s:16:\\\"Continue Selling\\\";}}i:3;a:2:{s:4:\\\"name\\\";s:14:\\\"Variant Option\\\";s:10:\\\"attributes\\\";a:6:{s:12:\\\"option1_name\\\";s:13:\\\"Option 1 Name\\\";s:13:\\\"option1_value\\\";s:14:\\\"Option 1 Value\\\";s:12:\\\"option2_name\\\";s:13:\\\"Option 2 Name\\\";s:13:\\\"option2_value\\\";s:14:\\\"Option 2 Value\\\";s:12:\\\"option3_name\\\";s:13:\\\"Option 3 Name\\\";s:13:\\\"option3_value\\\";s:14:\\\"Option 3 Value\\\";}}i:4;a:2:{s:4:\\\"name\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:10:\\\"attributes\\\";a:1:{i:1;s:23:\\\"Tanzayb Store Warehouse\\\";}}i:5;a:2:{s:4:\\\"name\\\";s:3:\\\"SEO\\\";s:10:\\\"attributes\\\";a:4:{s:7:\\\"seo_url\\\";s:8:\\\"URL Slug\\\";s:9:\\\"seo_title\\\";s:9:\\\"SEO Title\\\";s:15:\\\"seo_description\\\";s:15:\\\"SEO Description\\\";s:11:\\\"seo_keyword\\\";s:4:\\\"Tags\\\";}}}}s:22:\\\"converted_output_array\\\";a:6:{i:0;a:3:{s:5:\\\"label\\\";s:7:\\\"Default\\\";s:5:\\\"title\\\";s:7:\\\"Default\\\";s:7:\\\"options\\\";a:5:{i:0;a:2:{s:5:\\\"label\\\";s:18:\\\"Product Identifier\\\";s:5:\\\"value\\\";s:14:\\\"Default,handle\\\";}i:1;a:2:{s:5:\\\"label\\\";s:14:\\\"Product Images\\\";s:5:\\\"value\\\";s:12:\\\"Default,file\\\";}i:2;a:2:{s:5:\\\"label\\\";s:6:\\\"Vendor\\\";s:5:\\\"value\\\";s:14:\\\"Default,vendor\\\";}i:3;a:2:{s:5:\\\"label\\\";s:5:\\\"Brand\\\";s:5:\\\"value\\\";s:13:\\\"Default,brand\\\";}i:4;a:2:{s:5:\\\"label\\\";s:8:\\\"Category\\\";s:5:\\\"value\\\";s:18:\\\"Default,categories\\\";}}}i:1;a:3:{s:5:\\\"label\\\";s:7:\\\"General\\\";s:5:\\\"title\\\";s:7:\\\"General\\\";s:7:\\\"options\\\";a:2:{i:0;a:2:{s:5:\\\"label\\\";s:12:\\\"Product Name\\\";s:5:\\\"value\\\";s:20:\\\"General,product_name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:11:\\\"Description\\\";s:5:\\\"value\\\";s:19:\\\"General,description\\\";}}}i:2;a:3:{s:5:\\\"label\\\";s:7:\\\"Variant\\\";s:5:\\\"title\\\";s:7:\\\"Variant\\\";s:7:\\\"options\\\";a:11:{i:0;a:2:{s:5:\\\"label\\\";s:3:\\\"SKU\\\";s:5:\\\"value\\\";s:11:\\\"Variant,sku\\\";}i:1;a:2:{s:5:\\\"label\\\";s:4:\\\"Name\\\";s:5:\\\"value\\\";s:12:\\\"Variant,name\\\";}i:2;a:2:{s:5:\\\"label\\\";s:5:\\\"Image\\\";s:5:\\\"value\\\";s:12:\\\"Variant,file\\\";}i:3;a:2:{s:5:\\\"label\\\";s:5:\\\"Price\\\";s:5:\\\"value\\\";s:13:\\\"Variant,price\\\";}i:4;a:2:{s:5:\\\"label\\\";s:16:\\\"Compare at Price\\\";s:5:\\\"value\\\";s:24:\\\"Variant,compare_at_price\\\";}i:5;a:2:{s:5:\\\"label\\\";s:10:\\\"Cost Price\\\";s:5:\\\"value\\\";s:18:\\\"Variant,cost_price\\\";}i:6;a:2:{s:5:\\\"label\\\";s:13:\\\"UPC \\/ Barcode\\\";s:5:\\\"value\\\";s:15:\\\"Variant,barcode\\\";}i:7;a:2:{s:5:\\\"label\\\";s:6:\\\"Weight\\\";s:5:\\\"value\\\";s:14:\\\"Variant,weight\\\";}i:8;a:2:{s:5:\\\"label\\\";s:11:\\\"Weight Unit\\\";s:5:\\\"value\\\";s:19:\\\"Variant,weight_unit\\\";}i:9;a:2:{s:5:\\\"label\\\";s:14:\\\"Track Quantity\\\";s:5:\\\"value\\\";s:22:\\\"Variant,track_quantity\\\";}i:10;a:2:{s:5:\\\"label\\\";s:16:\\\"Continue Selling\\\";s:5:\\\"value\\\";s:24:\\\"Variant,continue_selling\\\";}}}i:3;a:3:{s:5:\\\"label\\\";s:14:\\\"Variant Option\\\";s:5:\\\"title\\\";s:14:\\\"Variant Option\\\";s:7:\\\"options\\\";a:6:{i:0;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 1 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option1_name\\\";}i:1;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 1 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option1_value\\\";}i:2;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 2 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option2_name\\\";}i:3;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 2 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option2_value\\\";}i:4;a:2:{s:5:\\\"label\\\";s:13:\\\"Option 3 Name\\\";s:5:\\\"value\\\";s:27:\\\"Variant Option,option3_name\\\";}i:5;a:2:{s:5:\\\"label\\\";s:14:\\\"Option 3 Value\\\";s:5:\\\"value\\\";s:28:\\\"Variant Option,option3_value\\\";}}}i:4;a:3:{s:5:\\\"label\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:5:\\\"title\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:7:\\\"options\\\";a:1:{i:0;a:2:{s:5:\\\"label\\\";s:23:\\\"Tanzayb Store Warehouse\\\";s:5:\\\"value\\\";s:28:\\\"Inventory -> Tanzayb Store,1\\\";}}}i:5;a:3:{s:5:\\\"label\\\";s:3:\\\"SEO\\\";s:5:\\\"title\\\";s:3:\\\"SEO\\\";s:7:\\\"options\\\";a:4:{i:0;a:2:{s:5:\\\"label\\\";s:8:\\\"URL Slug\\\";s:5:\\\"value\\\";s:11:\\\"SEO,seo_url\\\";}i:1;a:2:{s:5:\\\"label\\\";s:9:\\\"SEO Title\\\";s:5:\\\"value\\\";s:13:\\\"SEO,seo_title\\\";}i:2;a:2:{s:5:\\\"label\\\";s:15:\\\"SEO Description\\\";s:5:\\\"value\\\";s:19:\\\"SEO,seo_description\\\";}i:3;a:2:{s:5:\\\"label\\\";s:4:\\\"Tags\\\";s:5:\\\"value\\\";s:15:\\\"SEO,seo_keyword\\\";}}}}s:12:\\\"mapping_data\\\";a:4:{i:0;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:20:\\\"Default,Product Name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:20:\\\"General,product_name\\\";}}i:1;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"Default,SKU\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Variant,sku\\\";}}i:2;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Default,Price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:13:\\\"Variant,price\\\";}}i:3;a:3:{s:4:\\\"from\\\";a:1:{i:0;s:16:\\\"Default,Quantity\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:0:{}}}}}s:4:\\\"user\\\";O:8:\\\"App\\\\User\\\":32:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:5:\\\"users\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-27 06:52:55\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 06:52:55\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-27 06:52:55\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 06:52:55\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:1:{s:17:\\\"email_verified_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:2:{s:19:\\\"unreadNotifications\\\";O:55:\\\"Illuminate\\\\Notifications\\\\DatabaseNotificationCollection\\\":2:{s:8:\\\"\\u0000*\\u0000items\\\";a:0:{}s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;}s:13:\\\"notifications\\\";O:55:\\\"Illuminate\\\\Notifications\\\\DatabaseNotificationCollection\\\":2:{s:8:\\\"\\u0000*\\u0000items\\\";a:4:{i:0;O:45:\\\"Illuminate\\\\Notifications\\\\DatabaseNotification\\\":30:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:13:\\\"notifications\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:364:\\\"[{\\\"subject\\\":\\\"Import CSV Products Queue\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your CSV file is now being processed by Apimio. We\\\\u2019ll notify you once all product data has been successfully imported.\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"View\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\",\\\"batch\\\":null}]\\\";s:7:\\\"read_at\\\";N;s:10:\\\"created_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:364:\\\"[{\\\"subject\\\":\\\"Import CSV Products Queue\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your CSV file is now being processed by Apimio. We\\\\u2019ll notify you once all product data has been successfully imported.\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"View\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\",\\\"batch\\\":null}]\\\";s:7:\\\"read_at\\\";N;s:10:\\\"created_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:34:39\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:0:{}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:0:{}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:0:{}s:10:\\\"\\u0000*\\u0000guarded\\\";a:0:{}}i:1;O:45:\\\"Illuminate\\\\Notifications\\\\DatabaseNotification\\\":30:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:13:\\\"notifications\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:724:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\"}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:57\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"739e8183-eb2e-4e6b-8bff-92c7008bb3cb\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:724:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_26_110912.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":\\\"1\\\"}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:57\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:0:{}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:0:{}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:0:{}s:10:\\\"\\u0000*\\u0000guarded\\\";a:0:{}}i:2;O:45:\\\"Illuminate\\\\Notifications\\\\DatabaseNotification\\\":30:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:13:\\\"notifications\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"d1e651e2-41ff-46f8-bfaa-1448bce822ce\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125712.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:54\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:0:{}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:0:{}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:0:{}s:10:\\\"\\u0000*\\u0000guarded\\\";a:0:{}}i:3;O:45:\\\"Illuminate\\\\Notifications\\\\DatabaseNotification\\\":30:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:13:\\\"notifications\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:6:\\\"string\\\";s:12:\\\"incrementing\\\";b:0;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:52\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:10:{s:2:\\\"id\\\";s:36:\\\"2db6769b-b1d5-400c-974f-244e7b85d1db\\\";s:15:\\\"organization_id\\\";i:1;s:7:\\\"user_id\\\";i:1;s:4:\\\"type\\\";s:36:\\\"App\\\\Notifications\\\\ApimioNotification\\\";s:15:\\\"notifiable_type\\\";s:8:\\\"App\\\\User\\\";s:13:\\\"notifiable_id\\\";i:1;s:4:\\\"data\\\";s:722:\\\"[{\\\"subject\\\":\\\"Your export CSV file is ready to download\\\",\\\"greeting\\\":\\\"Hi Bilal Arshad\\\",\\\"body\\\":\\\"Your ( custom ) Export CSV with version <b>EN-US<\\\\\\/b> is generated successfully<br><br>Please copy and paste the below URL into your web browser: <br><a href='http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx'>http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx<\\\\\\/a><br>\\\",\\\"thanks\\\":\\\"Thank you for using localhost:8000\\\",\\\"actionText\\\":\\\"Download CSV File\\\",\\\"actionURL\\\":\\\"http:\\\\\\/\\\\\\/localhost:8000\\\\\\/products\\\\\\/download?filename=temp_files%2Fcustom_en_us_products_2025_06_23_125447.xlsx\\\",\\\"user_id\\\":1,\\\"organization_id\\\":1}]\\\";s:7:\\\"read_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";s:10:\\\"created_at\\\";s:19:\\\"2025-06-26 11:09:52\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-27 07:33:08\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:2:{s:4:\\\"data\\\";s:5:\\\"array\\\";s:7:\\\"read_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:0:{}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:0:{}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:0:{}s:10:\\\"\\u0000*\\u0000guarded\\\";a:0:{}}}s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;}}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:2:{i:0;s:8:\\\"password\\\";i:1;s:14:\\\"remember_token\\\";}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:9:{i:0;s:5:\\\"fname\\\";i:1;s:5:\\\"lname\\\";i:2;s:5:\\\"email\\\";i:3;s:8:\\\"password\\\";i:4;s:17:\\\"verification_code\\\";i:5;s:5:\\\"phone\\\";i:6;s:17:\\\"email_verified_at\\\";i:7;s:15:\\\"shopify_shop_id\\\";i:8;s:10:\\\"last_login\\\";}s:10:\\\"\\u0000*\\u0000guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}s:20:\\\"\\u0000*\\u0000rememberTokenName\\\";s:14:\\\"remember_token\\\";s:14:\\\"\\u0000*\\u0000accessToken\\\";N;}s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:8:\\\"filename\\\";s:48:\\\"temp_files\\/error_products_2025_06_27_073426.xlsx\\\";s:5:\\\"batch\\\";s:36:\\\"9f40defe-f107-453e-9f00-fc7223b0ae3c\\\";s:15:\\\"notification_id\\\";s:36:\\\"fe48e0e5-847d-462e-8955-10e4c4cb6209\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.032026, "duration": 0.00888, "duration_str": "8.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.399, "width_percent": 18.601}]}, "models": {"data": {"Illuminate\\Notifications\\DatabaseNotification": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Product\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}}, "count": 7, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "Import CSV Products Queue", "headers": "From: Apimio Local <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: Import CSV Products Queue\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/import/step1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"success\"\n  ]\n]", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:4 [\n          \"Product Name\" => \"Product Name\"\n          \"SKU\" => \"SKU\"\n          \"Price\" => \"Price\"\n          \"Quantity\" => \"Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"Default,Product Name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Default,SKU\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Default,Price\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Quantity\"\n          \"value\" => \"Default,Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751009623_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751009623_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:4 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Product Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"General,product_name\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Variant,sku\"\n      ]\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Variant,price\"\n      ]\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Quantity\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]", "success": "Import started successfully. Refresh products page after some time."}, "request": {"data": {"status": "409 Conflict", "full_url": "http://localhost:8000/products/import", "action_name": "products.import", "controller_action": "App\\Http\\Controllers\\Product\\ImportController@import_csv", "uri": "POST products/import", "controller": "App\\Http\\Controllers\\Product\\ImportController@import_csv<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=644\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=644\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ImportController.php:644-818</a>", "middleware": "web, check_billing", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f40deff-7f98-4555-bf18-34ec2c10d3e8\" target=\"_blank\">View in Telescope</a>", "duration": "16.04s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-189646548 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>all_product_skus</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>testing-product</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>request_data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Product Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751009623_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751009623_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189646548\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-184048973 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2249526f-665d-4bbf-9125-65742334829b</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">694a4d9c-2ead-41c6-accb-e20750e9e972</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c381d306-5a4d-4a40-bc3a-cee8403559e3</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7d3dcbfb-172b-498b-a89d-c1a632ce2c3c</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>catalog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>temp_status</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n  \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Apimio Default</span>\"\n  \"<span class=sf-dump-key>temp_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>export_type</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751009623_datafile.csv</span>\"\n  \"<span class=sf-dump-key>ignore_unmapped</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184048973\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-653118511 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">739</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikp0NG9YSkdIKzdranZ3anBkcWMwbVE9PSIsInZhbHVlIjoiaWlydVRFejZBc2p6SmVYdHNHWmdDbS9TVzNGZEYyRVNGQWVUSFd3d21SQlNiQ05ETFZsekVzVUhRRDR2L0NMSGY3QVBlbEcrcXNuVVIwQ1BMV1VqNUpySUZTM1JlNkk0eGZsYlZ3N2JyYjZXSFBSckJXMkk0SWpoTm9McWdNWFUiLCJtYWMiOiI0OTFlY2FlYmE3N2QzZGIxNzg3NWMwOGJkMzZmMmRlZTk3NGJjNDUzNTFlN2E1ZjQ2OTZlZWQ0ZjA0N2UxOTA1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">0f4e2ee0f7e2ca9da665d2f8035743df</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/products/import/create-template</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJORzV3YXNOSkthSUNCa0JUYWVZclE9PSIsInZhbHVlIjoiZzB1K1FaMUVBTHRvaGQreFNOd1hRTytSSnpTY1lTU1BxRVRpNGpEL00xbkM2ZUNOWFdhbFc5Z1NkSWVtMjdxV1Vtbm5XekxTTzl0RXlwRUhsL1VxWmJybkppQlUxMmRIY0xYaEVoR0t4VUN0V3BNM1NUZGRYMHdsQ1lKdDRRWXo4akE3Z0c3LzhlR1JVY3E5OWFBN1hnPT0iLCJtYWMiOiJhODM4ZGFjODBjZDk4YzBkMjMxYWFiMzdkYWE3MWQ5ZGVhYTEwNjU1MTNhOTFmNDZjNDllN2MxNTQ4YzM1YjJhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikp0NG9YSkdIKzdranZ3anBkcWMwbVE9PSIsInZhbHVlIjoiaWlydVRFejZBc2p6SmVYdHNHWmdDbS9TVzNGZEYyRVNGQWVUSFd3d21SQlNiQ05ETFZsekVzVUhRRDR2L0NMSGY3QVBlbEcrcXNuVVIwQ1BMV1VqNUpySUZTM1JlNkk0eGZsYlZ3N2JyYjZXSFBSckJXMkk0SWpoTm9McWdNWFUiLCJtYWMiOiI0OTFlY2FlYmE3N2QzZGIxNzg3NWMwOGJkMzZmMmRlZTk3NGJjNDUzNTFlN2E1ZjQ2OTZlZWQ0ZjA0N2UxOTA1IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Ink0S0wrK1RkRmFpZy9XMFV6M0lPeWc9PSIsInZhbHVlIjoiREliS1FQbVpYaE9oNThadzF2K24zcEJjMk9VTWcwWkErWVJtQ2MwMExUMXJGN3N5UVhJQXByUElENHhFNityOTZya0JtZHhCR29ZQWlPRWtOMDFaSitTQ2F0VE5uNUJ1YkFPMmQyeVdEMFZ5K0VjNUNHb2tlL0dPQ3NjRzFSblEiLCJtYWMiOiI0MjNkNjMxMTk0ZTY5OGQxOTc2MWUwY2FmOGNkZmJhMjI5NDNiNDg1ZWY2MzgwYmU3MmQxNzE0ZThiYjMwZWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653118511\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1944052470 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2mWd79PpeLkLTlAr4CMlCbKJn5gEwhRI3ndPAwIe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944052470\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-372153469 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia-location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 07:34:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372153469\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1162914790 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/products/import/step1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Product Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751009623_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751009623_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"67 characters\">Import started successfully. Refresh products page after some time.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162914790\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "409 Conflict", "full_url": "http://localhost:8000/products/import", "action_name": "products.import", "controller_action": "App\\Http\\Controllers\\Product\\ImportController@import_csv"}, "badge": "409 Conflict"}}