<?php ?>

<?php $__env->startSection('titles','Notification'); ?>
<?php $__env->startSection('content'); ?>
    
    <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => ''.e(trans('notification.page_title')).'','description' => '','links' => 'false','button' => 'false'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
    
    <ul class="nav nav-pills border-bottom" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications"
                    type="button" role="tab" aria-controls="notifications" aria-selected="true">Notifications
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="Activity_log-tab" data-bs-toggle="tab" data-bs-target="#Activity_log"
                    type="button" role="tab" aria-controls="profile" aria-selected="false">Activity log
            </button>
        </li>
    </ul>
    <div class="tab-content mt-2" id="myTabContent">
        
        <div class="tab-pane fade show active" id="notifications" role="tabpanel" aria-labelledby="home-tab">
            
            <div class="row">
                <?php if($notifications->isNotEmpty()): ?>
                    <div class="col-12 col-xl-7">
                        <ul class="notification-css">
                            <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <h3><?php echo $notification->data['subject']; ?></h3>
                                    <p class="mb-2"><?php echo $notification->data['body']; ?></p>
                                    <?php if(isset($notification->data['batch']['batch_progress'])): ?>
                                        <div class="notification-queue" data-batchId="<?php echo e($notification->data['batch']['id']); ?>">
                                            <div>
                                                <?php if($notification->data['batch']['batch_progress'] != 100): ?>
                                                    <a href="<?php echo e(url()->full()); ?>" class="text-decoration-none me-2 reload-page-js d-none" title="Reload the page to update response">
                                                        <i class="fa-sharp fa-solid fa-rotate-right fs-14"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <span class="batch-status-js status <?php echo e($notification->data['batch']['batch_progress'] != 100 ? 'status-publish' : 'status-success'); ?> notification-status-css">
                                                    <?php echo e($notification->data['batch']['batch_progress'] != 100 ? 'Processing' : 'Processed'); ?>

                                                </span>

                                            </div>
                                            <?php if($notification->data['batch']['batch_progress'] != 100): ?>
                                                <div class="main-progress-js d-flex align-items-center mt-3">
                                                    <div class="progress" style="height: 1rem;width: 60%;">
                                                        <div class="batch-progress-js progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: <?php echo e($notification->data['batch']['batch_progress']); ?>%"></div>
                                                    </div>
                                                    <div class="ms-3">
                                                        <span class="status-publish p-1 px-2 rounded fs-12 batch-progress-number-js"><?php echo e($notification->data['batch']['batch_progress']); ?>%</span>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="status status-success notification-status-css"><?php echo e($notification->data['batch']['id'] ?? 'info'); ?></span>
                                    <?php endif; ?>
                                    <p class="clr-grey mt-3 fs-14"><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                    <hr>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php else: ?>
                    <?php if (isset($component)) { $__componentOriginal9c590450895f60ce3af8182e3a1a843e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c590450895f60ce3af8182e3a1a843e = $attributes; } ?>
<?php $component = App\View\Components\General\EmptyPage::resolve(['description' => ''.e(trans('notification.page_empty')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.empty-page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\EmptyPage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $attributes = $__attributesOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $component = $__componentOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__componentOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
            
        </div>
        
        <div class="tab-pane fade" id="Activity_log" role="tabpanel" aria-labelledby="profile-tab">
            <div class="row">
                <div class="col-12 col-lg-12">
                
                    
                    <?php if(count($logs) > 0): ?>
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">S#</th>
                                <th scope="col" style="width: 60%;padding: 0px 10px">Message</th>
                                <th scope="col">Type</th>
                                <th scope="col">Status</th>
                                <th scope="col">Date & Time</th>
                                <th scope="col" class="text-end">Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            
                            <?php $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="align-top pt-4"><?php echo e($loop->iteration); ?></td>
                                    <td class="pt-1"><?php echo $log->description; ?></td>
                                    <td class="align-top pt-4"><?php echo e($log->type); ?></td>
                                    <td class="align-top pt-4">
                                        <?php if($log->status == 'warning'): ?>
                                            <span class="status status-warning"><?php echo e($log->status); ?></span>
                                        <?php elseif($log->status == 'success'): ?>
                                            <span class="status status-success"><?php echo e($log->status); ?></span>
                                        <?php elseif($log->status == 'error'): ?>
                                            <span class="status status-danger"><?php echo e($log->status); ?></span>
                                        <?php else: ?>
                                            <span class="status status-publish"><?php echo e($log->status); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-top pt-4" style="width: 15%;"><?php echo e($log->created_at->format('d M Y H:i a')); ?></td>
                                    <td class="align-top pt-4 text-end" style="width: 25%;"><a class="btn-sm btn-primary me-1" href="<?php echo e($log->link); ?>"><?php echo e($log->link_text); ?></a></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        
                    <?php else: ?>
                        <?php if (isset($component)) { $__componentOriginal9c590450895f60ce3af8182e3a1a843e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c590450895f60ce3af8182e3a1a843e = $attributes; } ?>
<?php $component = App\View\Components\General\EmptyPage::resolve(['description' => ''.e(trans('notification.logs_page_empty')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.empty-page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\EmptyPage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $attributes = $__attributesOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $component = $__componentOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__componentOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    </div>

    
    
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/notification/index.blade.php ENDPATH**/ ?>