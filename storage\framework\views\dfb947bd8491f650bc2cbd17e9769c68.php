
<?php $__env->startSection('titles','Mapping Fields'); ?>
<?php $__env->startSection('content'); ?>
<style>
.modal:after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.7;
}
</style>
<div id="importmapping" ></div>

<?php if(App::environment('local') && file_exists(base_path('packages/apimio/mapping-fields-package/resources/js/components/MappingModuleRedux.jsx'))): ?>
    <?php echo app('Illuminate\Foundation\Vite')("packages/apimio/mapping-fields-package/resources/js/components/MappingModuleRedux.jsx"); ?>
<?php else: ?>
    <?php echo app('Illuminate\Foundation\Vite')("vendor/apimio/mapping-connector-package/resources/js/components/MappingModuleRedux.jsx"); ?>
<?php endif; ?>

<?php $__env->startPush('footer_scripts'); ?>
        <script src="<?php echo e(asset('mapping-fields/js/jquery.insert-at-cursor.min.js')); ?>"></script>

        <script>
            $(document).ready(function () {
                $(document).scroll(function () {
                    var scrollPosition = $(this).scrollTop(); // Define scrollPosition inside the scroll function
                    var productHeader = $(".product-header");

                    if (scrollPosition >= 10) {
                        var width = $('#add_row').width() + 47;
                        productHeader.addClass("fixed-header shadow-md");
                        productHeader.css("width", width);
                    } else {
                        productHeader.removeClass("fixed-header shadow-md");
                        productHeader.removeAttr("style");
                    }

                });

            })

        </script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

    <script>
        var data = <?php echo json_encode($data, 15, 512) ?>;
        console.log(data);
        window.mappingData = data ;
    </script>

<script src="https://cdn.tailwindcss.com"></script>

<?php echo $__env->make('mapping::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\mapping-fields-package\src/views/mappingreact.blade.php ENDPATH**/ ?>