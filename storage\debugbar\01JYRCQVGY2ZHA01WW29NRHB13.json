{"__meta": {"id": "01JYRCQVGY2ZHA01WW29NRHB13", "datetime": "2025-06-27 09:50:44", "utime": **********.256827, "method": "GET", "uri": "/api/2024-12/image-quality-score", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751017842.926084, "end": **********.256857, "duration": 1.330772876739502, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1751017842.926084, "relative_start": 0, "end": **********.145502, "relative_end": **********.145502, "duration": 1.****************, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.14552, "relative_start": 1.****************, "end": **********.25686, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.176083, "relative_start": 1.****************, "end": **********.182697, "relative_end": **********.182697, "duration": 0.006613969802856445, "duration_str": "6.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.249022, "relative_start": 1.****************, "end": **********.249242, "relative_end": **********.249242, "duration": 0.0002200603485107422, "duration_str": "220μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.251419, "relative_start": 1.****************, "end": **********.251525, "relative_end": **********.251525, "duration": 0.00010585784912109375, "duration_str": "106μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/image-quality-score", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:157-180</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0093, "accumulated_duration_str": "9.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.213097, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 32.151}, {"sql": "select * from `files` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.235321, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:159", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=159", "ajax": false, "filename": "DashboardController.php", "line": "159"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.151, "width_percent": 67.849}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/image-quality-score\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/image-quality-score", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore", "uri": "GET api/2024-12/image-quality-score", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:157-180</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f410fa9-0eb5-4815-a6bf-ebbc177620c1\" target=\"_blank\">View in Telescope</a>", "duration": "1.34s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1230051009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1230051009\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2108252627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2108252627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-217707370 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilp4QzRubXEySk91SVY4UDlHdW5KaFE9PSIsInZhbHVlIjoiaVNYSVRaZGY5eENpRXVxalF4STQ4M1JiSEJqYmtXV0hUdXBOakNQSnpaMjFZdFRCRE9MS2MzQjB1cWVWMkxEdURiM1UwMGE2OG5vS3ZVaEl6QVZ0TEczL1ZIRTJUYW1hMGdieWVPYkcxZnVsaWtmZjIvL0FYUFdIZ2dxRVg3ZW4iLCJtYWMiOiI2MDBiNTBkNGIwODE1MWJlNmMzYTQ2ZGIwNmY3MmNmMWI5MjcyN2M2YTFhMTVhNzI0NjIwMGFkZjY4ZTBjZTk4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVzOU9OS2dHeWtJNXFnajdrWTdJNEE9PSIsInZhbHVlIjoiUGs5TEhWbFVSdFJrR1VDVGJ0UVlLRnMrWjdjZm9mS1AwZXNOL0Ixakxtazc2aWUrZzVtRXhhN2xERmRCT3NodFRtd2dvODZRdHRZZmpwNDhWcmdBakl3R0U5SGI0TmFkbGNZWTVTVkhrclZCZGk2eWo0emNsSFJ3T1Q0THpXZXYyeExTRitjclJSK1NVTmpwUkFOSG9BPT0iLCJtYWMiOiJkMjNhODZkNzc0ZDNiMDQ3NjhlNWU0MDcwNDVjMWJmM2RhNTkxODk1MzNiN2M2OTkxMDQyNmVlNmU2OGI5M2ZhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilp4QzRubXEySk91SVY4UDlHdW5KaFE9PSIsInZhbHVlIjoiaVNYSVRaZGY5eENpRXVxalF4STQ4M1JiSEJqYmtXV0hUdXBOakNQSnpaMjFZdFRCRE9MS2MzQjB1cWVWMkxEdURiM1UwMGE2OG5vS3ZVaEl6QVZ0TEczL1ZIRTJUYW1hMGdieWVPYkcxZnVsaWtmZjIvL0FYUFdIZ2dxRVg3ZW4iLCJtYWMiOiI2MDBiNTBkNGIwODE1MWJlNmMzYTQ2ZGIwNmY3MmNmMWI5MjcyN2M2YTFhMTVhNzI0NjIwMGFkZjY4ZTBjZTk4IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkhDK0lBWUVtQ0tnNEIzSm83RElPUUE9PSIsInZhbHVlIjoiZnYzQXBORm5PVkh0QnhLVUxxaGlZNnNhbmRFaDJkU2w1d2JMZzRHYXYrZGV6Y2svbWtIc2lPUDNkaUkvOVREcENqNHEySG8waXp5SlFORnVUa1h6VHJsbVBxQ3BBT0dVSENzcjdXbytPOEtDTy8vUE1pa1RCWkxhMFpBUy9kR3YiLCJtYWMiOiI0YmU2MTgzNzQ3NjI0NGJlNzJiODc2MDA3ZGEzNGYzZmQ3MjI5ZDk2ODZiN2NkY2ZmNjM2Mzk0ZmFjZmQ5ODU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217707370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1837553748 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|IbeS3I2W02eAkiae8I30deZtxSJuBVzomEN4VW4GfzaWyD7XMplnXtrAPk3Q|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o4qeOX4JeNGwBuCfe3fqu5iqZVUYsFaZmtFfdug8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837553748\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1323763471 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:50:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxwTlE0cFUzZkd4dW1ydk1DSFZtSkE9PSIsInZhbHVlIjoiWm5rNnI3WmNDdy9XNHMxVW9YSTBsbVlRY3ZFWVZrVmZpNDhrRVFpazdHWHNaMW5PSDFTUFhqeWI5aENVclEwM3UzWG1IdUpJV2pVWUFwOWtJc2xGRUk2cGpXVGpBbEEwbGYyY0VPS2ZoTTRyNmtkU05JK1lYYk8vZ2dleDl4b1ciLCJtYWMiOiJiZDZlYmUwNzhkYTU2N2VjMzE2N2ZiNDU0NWZmZWVmZDFmYmNkNTRkNmQ0MDkzOGM5NDY0YTkyMTNjOGJkOTYwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:50:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6InY3MFpYM3pObFk1MUtjekY4RTJjTWc9PSIsInZhbHVlIjoiMEVWZENTMFV3RysvLzE5eERrNjZtZjZSV0p4NE9Xb1Z4dXlGa1pqS2dQd1lIcUMzYkVMMW5hUmlFbHJYU0JieWpwRFpkZ2duQXlVQUFNMkhYbDNwVzFJNFU0NE9GSXYrcnNOc0JkKzZXbUdMRXhRTXdubEZyODV6ZEd0WEtQdEwiLCJtYWMiOiJkM2RkMTI1YmNjZDJkOWYyZmU2ODk3ODQ5OGExZWZmZWJjYmVlNTZlYTI0YjQ0NDAwMDYyM2RiMDJhZDRiZTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:50:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxwTlE0cFUzZkd4dW1ydk1DSFZtSkE9PSIsInZhbHVlIjoiWm5rNnI3WmNDdy9XNHMxVW9YSTBsbVlRY3ZFWVZrVmZpNDhrRVFpazdHWHNaMW5PSDFTUFhqeWI5aENVclEwM3UzWG1IdUpJV2pVWUFwOWtJc2xGRUk2cGpXVGpBbEEwbGYyY0VPS2ZoTTRyNmtkU05JK1lYYk8vZ2dleDl4b1ciLCJtYWMiOiJiZDZlYmUwNzhkYTU2N2VjMzE2N2ZiNDU0NWZmZWVmZDFmYmNkNTRkNmQ0MDkzOGM5NDY0YTkyMTNjOGJkOTYwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:50:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6InY3MFpYM3pObFk1MUtjekY4RTJjTWc9PSIsInZhbHVlIjoiMEVWZENTMFV3RysvLzE5eERrNjZtZjZSV0p4NE9Xb1Z4dXlGa1pqS2dQd1lIcUMzYkVMMW5hUmlFbHJYU0JieWpwRFpkZ2duQXlVQUFNMkhYbDNwVzFJNFU0NE9GSXYrcnNOc0JkKzZXbUdMRXhRTXdubEZyODV6ZEd0WEtQdEwiLCJtYWMiOiJkM2RkMTI1YmNjZDJkOWYyZmU2ODk3ODQ5OGExZWZmZWJjYmVlNTZlYTI0YjQ0NDAwMDYyM2RiMDJhZDRiZTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:50:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323763471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-478502482 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/image-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478502482\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/image-quality-score", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore"}, "badge": null}}