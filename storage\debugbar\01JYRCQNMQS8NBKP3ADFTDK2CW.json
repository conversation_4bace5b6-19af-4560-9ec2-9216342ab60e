{"__meta": {"id": "01JYRCQNMQS8NBKP3ADFTDK2CW", "datetime": "2025-06-27 09:50:38", "utime": **********.23311, "method": "GET", "uri": "/api/2024-12/organization/billing/status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751017837.008501, "end": **********.233156, "duration": 1.2246549129486084, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1751017837.008501, "relative_start": 0, "end": **********.031259, "relative_end": **********.031259, "duration": 1.****************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.031278, "relative_start": 1.****************, "end": **********.233159, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.066754, "relative_start": 1.****************, "end": **********.077084, "relative_end": **********.077084, "duration": 0.010329961776733398, "duration_str": "10.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.224184, "relative_start": 1.****************, "end": **********.224479, "relative_end": **********.224479, "duration": 0.0002949237823486328, "duration_str": "295μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.227622, "relative_start": 1.***************, "end": **********.227738, "relative_end": **********.227738, "duration": 0.00011587142944335938, "duration_str": "116μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization/billing/status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "as": "organization.billing.status", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016859999999999997, "accumulated_duration_str": "16.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.125939, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 42.883}, {"sql": "select * from `organizations` where `id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.188849, "duration": 0.0069299999999999995, "duration_str": "6.93ms", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:177", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=177", "ajax": false, "filename": "OrganizationController.php", "line": "177"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.883, "width_percent": 41.103}, {"sql": "select count(*) as aggregate from `subscriptions` where `organization_id` = 1 and `stripe_status` = 'active'", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Traits/Billing/BillingTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Billing\\BillingTrait.php", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 188}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.205219, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "BillingTrait.php:27", "source": {"index": 15, "namespace": null, "name": "app/Traits/Billing/BillingTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Billing\\BillingTrait.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FBilling%2FBillingTrait.php&line=27", "ajax": false, "filename": "BillingTrait.php", "line": "27"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.986, "width_percent": 12.871}, {"sql": "select count(*) as aggregate from `shopify_subscriptions` where `organization_id` = 1 and `status` = 'active'", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Billing/BillingTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Billing\\BillingTrait.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 188}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.212996, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BillingTrait.php:31", "source": {"index": 16, "namespace": null, "name": "app/Traits/Billing/BillingTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Billing\\BillingTrait.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FBilling%2FBillingTrait.php&line=31", "ajax": false, "filename": "BillingTrait.php", "line": "31"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.856, "width_percent": 3.144}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization/billing/status\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus", "uri": "GET api/2024-12/organization/billing/status", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f410f9f-e19b-42d1-b319-421ed6bdd3f5\" target=\"_blank\">View in Telescope</a>", "duration": "1.23s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1794561198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1794561198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-40111229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-40111229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-578929489 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilp4QzRubXEySk91SVY4UDlHdW5KaFE9PSIsInZhbHVlIjoiaVNYSVRaZGY5eENpRXVxalF4STQ4M1JiSEJqYmtXV0hUdXBOakNQSnpaMjFZdFRCRE9MS2MzQjB1cWVWMkxEdURiM1UwMGE2OG5vS3ZVaEl6QVZ0TEczL1ZIRTJUYW1hMGdieWVPYkcxZnVsaWtmZjIvL0FYUFdIZ2dxRVg3ZW4iLCJtYWMiOiI2MDBiNTBkNGIwODE1MWJlNmMzYTQ2ZGIwNmY3MmNmMWI5MjcyN2M2YTFhMTVhNzI0NjIwMGFkZjY4ZTBjZTk4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVzOU9OS2dHeWtJNXFnajdrWTdJNEE9PSIsInZhbHVlIjoiUGs5TEhWbFVSdFJrR1VDVGJ0UVlLRnMrWjdjZm9mS1AwZXNOL0Ixakxtazc2aWUrZzVtRXhhN2xERmRCT3NodFRtd2dvODZRdHRZZmpwNDhWcmdBakl3R0U5SGI0TmFkbGNZWTVTVkhrclZCZGk2eWo0emNsSFJ3T1Q0THpXZXYyeExTRitjclJSK1NVTmpwUkFOSG9BPT0iLCJtYWMiOiJkMjNhODZkNzc0ZDNiMDQ3NjhlNWU0MDcwNDVjMWJmM2RhNTkxODk1MzNiN2M2OTkxMDQyNmVlNmU2OGI5M2ZhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilp4QzRubXEySk91SVY4UDlHdW5KaFE9PSIsInZhbHVlIjoiaVNYSVRaZGY5eENpRXVxalF4STQ4M1JiSEJqYmtXV0hUdXBOakNQSnpaMjFZdFRCRE9MS2MzQjB1cWVWMkxEdURiM1UwMGE2OG5vS3ZVaEl6QVZ0TEczL1ZIRTJUYW1hMGdieWVPYkcxZnVsaWtmZjIvL0FYUFdIZ2dxRVg3ZW4iLCJtYWMiOiI2MDBiNTBkNGIwODE1MWJlNmMzYTQ2ZGIwNmY3MmNmMWI5MjcyN2M2YTFhMTVhNzI0NjIwMGFkZjY4ZTBjZTk4IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkhDK0lBWUVtQ0tnNEIzSm83RElPUUE9PSIsInZhbHVlIjoiZnYzQXBORm5PVkh0QnhLVUxxaGlZNnNhbmRFaDJkU2w1d2JMZzRHYXYrZGV6Y2svbWtIc2lPUDNkaUkvOVREcENqNHEySG8waXp5SlFORnVUa1h6VHJsbVBxQ3BBT0dVSENzcjdXbytPOEtDTy8vUE1pa1RCWkxhMFpBUy9kR3YiLCJtYWMiOiI0YmU2MTgzNzQ3NjI0NGJlNzJiODc2MDA3ZGEzNGYzZmQ3MjI5ZDk2ODZiN2NkY2ZmNjM2Mzk0ZmFjZmQ5ODU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578929489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770259500 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|IbeS3I2W02eAkiae8I30deZtxSJuBVzomEN4VW4GfzaWyD7XMplnXtrAPk3Q|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o4qeOX4JeNGwBuCfe3fqu5iqZVUYsFaZmtFfdug8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770259500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-664579174 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:50:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNuUDhBV1BTb3AzNEJsNmVia3pKbGc9PSIsInZhbHVlIjoiRGt3bzN0YzdnTXhXamhtWGhGWGtLVWJMcGN5VFJhaXFVUmpydmRJcTd1MFQ3eWhUZE94dElWczlZbHhybWk1MWhEcUsyaDgrTDF2THBSOU13UlBLTWp6SmU4OTFGVDU2clZ1YmhaTW5Md0d6Y1U0QlZPR0xKZ3pBbVZzS0dyNmUiLCJtYWMiOiI2NzIzMzJhZDRlNjc3YTkyZTQ2NjZlY2Y0NmJhZGU3ZTkwYjYxYjhiZWI0ODRkODZlZTQ2MTM4MjAyNTUyNzYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:50:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6InNqbzFMQWpSSTdVWFNQd3NHNEFNUXc9PSIsInZhbHVlIjoiRnBDVGxpYzlpcTlsMGd3ZFFCTjRXS3oycVpOT0lkblp0YzV5cE5ESFRUVnV1TVEya1ZPaC9iajM0eXkvTDhoZHJzTzlHQ09LTXVQbTA0OVNNWFlCN0FtMy80Q1FQZ2FqdW9yNWY5eFR6ZlBLZmxiQnFmRkM5UkNTc1RiWGsxd24iLCJtYWMiOiIwOWY2YjZkOGMwNDhmMjliODBkZGZlNmMzZDg4MzQ4ZjgyYzM5OTM3ODFjNWY2M2M2YWIxYWM0OTdhZTA1ZTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:50:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNuUDhBV1BTb3AzNEJsNmVia3pKbGc9PSIsInZhbHVlIjoiRGt3bzN0YzdnTXhXamhtWGhGWGtLVWJMcGN5VFJhaXFVUmpydmRJcTd1MFQ3eWhUZE94dElWczlZbHhybWk1MWhEcUsyaDgrTDF2THBSOU13UlBLTWp6SmU4OTFGVDU2clZ1YmhaTW5Md0d6Y1U0QlZPR0xKZ3pBbVZzS0dyNmUiLCJtYWMiOiI2NzIzMzJhZDRlNjc3YTkyZTQ2NjZlY2Y0NmJhZGU3ZTkwYjYxYjhiZWI0ODRkODZlZTQ2MTM4MjAyNTUyNzYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:50:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6InNqbzFMQWpSSTdVWFNQd3NHNEFNUXc9PSIsInZhbHVlIjoiRnBDVGxpYzlpcTlsMGd3ZFFCTjRXS3oycVpOT0lkblp0YzV5cE5ESFRUVnV1TVEya1ZPaC9iajM0eXkvTDhoZHJzTzlHQ09LTXVQbTA0OVNNWFlCN0FtMy80Q1FQZ2FqdW9yNWY5eFR6ZlBLZmxiQnFmRkM5UkNTc1RiWGsxd24iLCJtYWMiOiIwOWY2YjZkOGMwNDhmMjliODBkZGZlNmMzZDg4MzQ4ZjgyYzM5OTM3ODFjNWY2M2M2YWIxYWM0OTdhZTA1ZTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:50:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664579174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-593291987 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8000/api/2024-12/organization/billing/status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593291987\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus"}, "badge": null}}