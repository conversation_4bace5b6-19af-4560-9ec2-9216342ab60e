import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Async thunk for fetching organization billing status
export const fetchOrganizationBillingStatus = createAsyncThunk(
    'organization/fetchBillingStatus',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get("/api/2024-12/organization/billing/status");
            return response.data;
        } catch (error) {
            console.error("Error fetching organization billing status:", error);
            console.error("Error response:", error.response);
            console.error("Error status:", error.response?.status);
            console.error("Error data:", error.response?.data);
            return rejectWithValue(error.response?.data?.message || `Failed to fetch organization billing status: ${error.response?.status} ${error.response?.statusText}`);
        }
    }
);

const initialState = {
    billingStatus: null,
    loading: false,
    error: null,
};

const organizationSlice = createSlice({
    name: 'organization',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
        resetBillingStatus: (state) => {
            state.billingStatus = null;
            state.loading = false;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchOrganizationBillingStatus.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchOrganizationBillingStatus.fulfilled, (state, action) => {
                state.loading = false;
                state.billingStatus = action.payload.billing_status;
                state.error = null;
            })
            .addCase(fetchOrganizationBillingStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

export const { clearError, resetBillingStatus } = organizationSlice.actions;

// Selectors
export const selectBillingStatus = (state) => state.organization.billingStatus;
export const selectOrganizationLoading = (state) => state.organization.loading;
export const selectOrganizationError = (state) => state.organization.error;

export default organizationSlice.reducer;
