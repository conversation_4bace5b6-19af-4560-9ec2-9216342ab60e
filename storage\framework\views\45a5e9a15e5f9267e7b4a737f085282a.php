<div class="modal fade" id="create_product" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title Poppins " id="exampleModalLabel" style="font-size: 20px">
                    <?php echo e(trans('products.add_product_modal_title')); ?>

                </h3>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 cancel-product-modal-js" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="pro_addpro_form" action="<?php echo e(route("products.store")); ?>" method="post" class="formStyle">
                    <?php echo csrf_field(); ?>
                    <div class="form-group mb-4">
                        <label for="sku"><?php echo e(trans('products.product_label')); ?>&nbsp;<span style="color: #ff8178">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['sku'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sku" name="sku" value="<?php echo e(old("sku")); ?>" required>
                        <?php $__errorArgs = ['sku'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger" role="alert">
                            <small><?php echo e($message); ?></small>
                        </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="formStyle" data-value="channels" style="display: none;">
                        <label for="" class="ml-1 Roboto bold text-dark"><?php echo e(trans('products_edit.store')); ?></label>
                        <select data-confirm-before-leave="true" placeholder="Select..." id="channels" multiple="multiple" name="channels[][id]" class="form-control hide sumoselect <?php $__errorArgs = ['channels'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__currentLoopData = $channels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $channel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($channel->id); ?>" class="Poppins regular text-color"
                                <?php echo e($loop->first?'selected':""); ?>

                                <?php echo e($channel->name); ?>

                            </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="form-group mt-2">
                        <button id="pro_addpro_btn" type="submit" class="btn btn-primary btn-block">
                            <?php echo e(trans('products.add_product_btn')); ?>

                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/components/products/add-product.blade.php ENDPATH**/ ?>