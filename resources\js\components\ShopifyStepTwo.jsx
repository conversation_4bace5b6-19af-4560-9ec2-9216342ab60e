import React, { useState } from "react";
import { <PERSON><PERSON>, message } from "antd";
import { router } from "@inertiajs/react";
import SyncProducts from "../../../public/v2/icons/syncproducts-icon.svg";
import CheckedIcon from "../../../public/v2/icons/checked-icon.svg";

const ShopifyStepTwo = ({ shopifyUrl, onSkip = null }) => {
    const [loading, setLoading] = useState(false);

    /**
     * Validates a Shopify store URL
     * @param {string} url - The URL to validate
     * @returns {boolean} - Whether the URL is valid
     */
    const validateShopifyUrl = (url) => {
        if (!url || !url.trim()) {
            return false;
        }

        try {
            // Handle URLs with or without protocol
            let cleanUrl = url.trim();

            // Remove protocol if present (http://, https://)
            cleanUrl = cleanUrl.replace(/^https?:\/\//, "");

            // Remove www. if present
            cleanUrl = cleanUrl.replace(/^www\./, "");

            // Remove trailing slash if present
            cleanUrl = cleanUrl.replace(/\/$/, "");

            // Check if the URL matches Shopify store pattern
            const shopifyPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/;
            return shopifyPattern.test(cleanUrl);
        } catch (error) {
            return false;
        }
    };

    /**
     * Handles the "Sync Products Now" button click.
     * Redirects the user to the backend endpoint to initiate the syncing process.
     */
    const handleSyncProducts = () => {
        if (!shopifyUrl.trim() || !validateShopifyUrl(shopifyUrl)) {
            message.error("Please provide a valid Shopify store URL.");
            return;
        }

        setLoading(true);

        // Process the Shopify URL to remove protocol (http:// or https://)
        const processedShopUrl = shopifyUrl.replace(/^https?:\/\//, "");

        // Prepare the query parameters using URLSearchParams for proper encoding
        const params = new URLSearchParams({
            shop: processedShopUrl,
            sync_product: "yes",
            should_not_billed: "1",
        });

        // Construct the full URL
        const syncUrl = `/channel/shopify/install?${params.toString()}`;

        // Redirect to the constructed URL
        window.location.href = syncUrl;

        // Optionally, you can handle loading state if the redirection is not immediate
        // For example, if the backend returns a response before redirecting
        // setLoading(false);
    };

    const handleSkip = () => {
        if (onSkip) {
            onSkip();
        } else {
            router.visit("/dashboard");
        }
    };

    return (
        <div className="flex-1 px-70 py-40 pb-120 bg-gray-100 min-h-screen">
            <h2 className="text-4xl text-center font-bold pb-10">Welcome to Apimio!</h2>
            <p className="text-gray-500 text-center mb-6">Your account is successfully created.</p>
            <div className="flex gap-4 max-w-3xl mx-auto justify-center items-center flex-col">
                <img src={SyncProducts} alt="Sync Products Icon" />
                <p className="text-3xl font-semibold leading-tight text-center">
                    Now it's time to sync your Shopify store with Apimio and manage your products seamlessly.
                </p>
            </div>
            <div className="flex justify-center mt-6 gap-8">
                <Button
                    type="primary"
                    className="bg-purple-700 text-sm rounded font-medium h-8 px-4 py-1 border border-purple-700 text-white"
                    onClick={handleSyncProducts}
                    loading={loading}
                >
                    Sync Products Now
                </Button>
            </div>
            <div className="flex justify-center mt-6">
                <button className="text-purple-700 text-sm" onClick={handleSkip}>
                    Skip for now
                </button>
            </div>
            <div className="flex max-w-3xl mx-auto items-start mt-5">
                <div className="mt-1">
                    <img src={CheckedIcon} alt="Checked Icon" />
                </div>
                <div className="pl-3">
                    <p className="text-lg font-bold">What happens next?</p>
                    <p className="text-gray-600 text-sm pt-2">
                        Once synced, your Shopify products will automatically import into Apimio. You can then manage your product data
                        efficiently in one place.
                    </p>
                </div>
            </div>
            <div className="flex max-w-3xl mx-auto items-start pt-5">
                <div className="mt-1">
                    <img src={CheckedIcon} alt="Checked Icon" />
                </div>
                <div className="pl-3">
                    <p className="text-lg font-bold">Benefits of Syncing with Apimio</p>
                    <ul className="list-disc pl-4 text-gray-600">
                        <li className="pt-2">
                            <span className="font-semibold">Centralized Product Management:</span> Control all your product data in one
                            platform, making management easier and faster.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Seamless Channel Sync:</span> Ensure consistency by syncing product data across
                            multiple channels, including Shopify and more.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Improve Data Accuracy:</span> Eliminate errors and inconsistencies with Apimio's
                            powerful data validation tools.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Bulk Editing Tools:</span> Make large-scale edits to your product catalog
                            quickly and efficiently.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Automated Updates:</span> Save time by setting up automated product data
                            updates.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default ShopifyStepTwo;
