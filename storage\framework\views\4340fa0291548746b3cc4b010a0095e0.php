<?php ?>

<?php $__env->startSection('titles','Export Products'); ?>
<?php $__env->startSection('content'); ?>
    <?php $__env->startPush('header_scripts'); ?>

        <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet" />
        <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">

        <style>
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
            input[type="file"] {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0,0,0,0);
                border: 0;
            }


        </style>
    <?php $__env->stopPush(); ?>

    <div>
        <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => ''.e(trans('products_export_step1.page_title')).'','description' => ''.e(trans('products_export_step1.page_description')).'','links' => 'false','button' => 'true'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['buttonname' => 'null']); ?>
         <?php $__env->slot('addbutton', null, []); ?> 

                <a href="<?php echo e(route('products.index')); ?>" id="cancel-btn"
                                   class="btn btn-outline-danger only-disabled" id="cancel-btn">
                                    <?php echo e(trans('products_export_step1.cancel_btn')); ?>

                                </a>
                                <button type="button" form="product-form-listing" id="export-one-next-btn"
                                        class="btn btn-primary ms-2">
                                    <?php echo e(trans('products_export_step1.next_btn')); ?>


                                </button>

             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>

        <form id="export-product-form-listing" method="POST" action="">
            <input type="hidden" name="filter[filter_export_productIds]" id="export_filter_bulk_productIds" value=""/>
            <input type="hidden" name="filter[filter_total_product_array]" id="export_filter_total_product_array" value=""/>

        </form>

        <div class="card border-radius shadow-none mb-5 d-none">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-around">
                            <div class="line"></div>
                            <!--blue1-->
                            <div id="blueOne" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-primary text-center Roboto"><?php echo e(__("1")); ?></span>
                                        <p class="Roboto bold mt-3 mb-0" ><?php echo e(trans('general.step1_title')); ?></p>
                                        <p class="Roboto regular m-0" ><?php echo e(trans('products_export_step1.step1_description')); ?></p>
                                    </div>
                                </div>
                            </div>
                            <!--grey2-->
                            <div id="greyTwo" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-outline-primary text-center Roboto"><?php echo e(__("2")); ?></span>
                                        <p class="Roboto bold mt-3 mb-0" ><?php echo e(trans('general.step2_title')); ?></p>
                                        <p class="Roboto regular m-0" ><?php echo e(trans('products_export_step1.step2_description')); ?></p>
                                    </div>
                                </div>
                            </div>
                            <!--grey3-->
                            <div id="greyThree" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-outline-primary text-center Roboto"><?php echo e(__("3")); ?></span>
                                        <p class="Roboto bold mt-3 mb-0" ><?php echo e(trans('general.step3_title')); ?></p>
                                        <p class="Roboto regular m-0" ><?php echo e(trans('products_export_step1.step3_description')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>

        <!--List Table-->
        <div class="container-fluid p-0" style="max-height: 400px">
            <div id="listingTable" data-delete-product-route="<?php echo e(route('products.destroy', ':id')); ?>"></div>
                     <?php echo app('Illuminate\Foundation\Vite')("resources/js/components/productListing/ListingTable.jsx"); ?>
        </div>
        

    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer_scripts'); ?>

    <script>
        // let export_types = {};
        // function all_export_types(export_type) {
        //     export_type.shopify = 'Shopify',
        //     export_type.magento = 'Magento',
        //     export_type.custom = 'Custom'
        // }


        // all_export_types(export_types);


        $(document).ready(function () {

            $(".clear-all-products").attr('href','<?php echo e(route('export.exportOne')); ?>');

            $('#export-one-next-btn').on('click',function (e) {
                e.preventDefault();

                const selectedCheckboxes = $('.bulk_product_check:checkbox:checked');
                const selectedIDs = selectedCheckboxes.map(function() {
                    return $(this).attr('id');
                }).get();
                const totalCount = parseInt($('#totalCount').text(), 10);
                if (totalCount === selectedCheckboxes.length) {
                    $('#export_filter_bulk_productIds').val(selectedIDs);
                    $('#export_filter_total_product_array').val($('#filter_total_product_array').val());
                } else{
                    $('#export_filter_bulk_productIds').val('all');
                    $('#export_filter_total_product_array').val($('#filter_total_product_array').val());

                }

                if ($('.ant-table-tbody').length > 0 && $('.ant-table-tbody tr.ant-table-row.ant-table-row-level-0').length > 0) {
                    $("#export-product-form-listing").attr('action','<?php echo e(route('export.exportTwo')); ?>');
                    $('#export-product-form-listing').submit();
                }
                else{
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Please export at least one product',
                    })
                }




                    // Swal.fire({
                    //     title: 'EXPORT TYPE',
                    //     input: 'select',
                    //     inputOptions: export_types,
                    //     inputPlaceholder: 'Choose',
                    //     icon: 'info',
                    //     showCancelButton: true,
                    //     confirmButtonColor: '#3085d6',
                    //     cancelButtonColor: '#d33',
                    //     confirmButtonText: 'Next',
                    //     reverseButtons : true,
                    //     allowOutsideClick: () => !Swal.isLoading(),
                    //     preConfirm: (test) => {
                    //         if(test == "") Swal.showValidationMessage("please select your export type");
                    //     }
                    // }).then((result) => {
                    //     if (result.isConfirmed) {
                    //         $('<input>').attr({
                    //             type: 'hidden',
                    //             value: result.value,
                    //             name: 'export_type'
                    //         }).appendTo('#product-form-listing');
                    //         $('#product-form-listing').submit();
                    //     }
                    // })
            })

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/export/exportOne.blade.php ENDPATH**/ ?>