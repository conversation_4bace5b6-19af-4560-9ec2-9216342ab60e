import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

// Import reducers
import counterReducer from './slices/counterSlice';
import authReducer from './slices/authSlice';
import mappingReducer from './slices/mappingSlice';
import productReducer from './slices/productSlice';
import organizationReducer from './slices/organizationSlice';

export const store = configureStore({
    reducer: {
        // Add reducers here as they're created
        counter: counterReducer,
        auth: authReducer,
        mapping: mappingReducer,
        products: productReducer,
        organization: organizationReducer,
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
        }),
    devTools: process.env.NODE_ENV !== 'production',
});

// Optional, but useful for refetchOnFocus/refetchOnReconnect behaviors
setupListeners(store.dispatch);

export default store;
