{"__meta": {"id": "01JYRBQ67GZMSFAGBANFNM48X5", "datetime": "2025-06-27 09:32:53", "utime": **********.873956, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:32:53] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.845635, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.083318, "end": **********.87399, "duration": 0.7906720638275146, "duration_str": "791ms", "measures": [{"label": "Booting", "start": **********.083318, "relative_start": 0, "end": **********.769428, "relative_end": **********.769428, "duration": 0.****************, "duration_str": "686ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.769455, "relative_start": 0.****************, "end": **********.873993, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.786892, "relative_start": 0.****************, "end": **********.791773, "relative_end": **********.791773, "duration": 0.004881143569946289, "duration_str": "4.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.861037, "relative_start": 0.****************, "end": **********.861487, "relative_end": **********.861487, "duration": 0.00044989585876464844, "duration_str": "450μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.868683, "relative_start": 0.***************, "end": **********.868857, "relative_end": **********.868857, "duration": 0.00017380714416503906, "duration_str": "174μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:182-219</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00566, "accumulated_duration_str": "5.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8221939, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 54.064}, {"sql": "select * from `channels` where `organization_id` = 1 and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8513951, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:186", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=186", "ajax": false, "filename": "DashboardController.php", "line": "186"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.064, "width_percent": 45.936}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "uri": "GET api/2024-12/shopify-sync-status", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:182-219</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f410947-c92c-44d8-ba16-fe3929f7085d\" target=\"_blank\">View in Telescope</a>", "duration": "792ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-63190855 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-63190855\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2065952422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2065952422\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2116684155 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik05bEM2b3Z2SGJmOWhMYVBidno1UlE9PSIsInZhbHVlIjoiM3RRNTJsR3NkWSs3WkxGL0xZL0E0Z21EalRUVVp3STNGZHdBYnYzNnRjTlNYNkJVTjZVcnJoclZHMy9NbjhEMFJJZnRBUUdwTHNVSmZyNjN1NldvTGpQZVpkaVcvekhmdmppT1VKd2JDcUlmUnAzY0pDc254QStzTldwdi9vdUwiLCJtYWMiOiJjMjJkMDVkMWM4MzNiMTMzNzUyNTU0MjIwN2NlYTJhYTQ2NWFiMGU3NmMzMTNkNTFiMTEzM2MyZDMwMzg1NTE2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVzOU9OS2dHeWtJNXFnajdrWTdJNEE9PSIsInZhbHVlIjoiUGs5TEhWbFVSdFJrR1VDVGJ0UVlLRnMrWjdjZm9mS1AwZXNOL0Ixakxtazc2aWUrZzVtRXhhN2xERmRCT3NodFRtd2dvODZRdHRZZmpwNDhWcmdBakl3R0U5SGI0TmFkbGNZWTVTVkhrclZCZGk2eWo0emNsSFJ3T1Q0THpXZXYyeExTRitjclJSK1NVTmpwUkFOSG9BPT0iLCJtYWMiOiJkMjNhODZkNzc0ZDNiMDQ3NjhlNWU0MDcwNDVjMWJmM2RhNTkxODk1MzNiN2M2OTkxMDQyNmVlNmU2OGI5M2ZhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik05bEM2b3Z2SGJmOWhMYVBidno1UlE9PSIsInZhbHVlIjoiM3RRNTJsR3NkWSs3WkxGL0xZL0E0Z21EalRUVVp3STNGZHdBYnYzNnRjTlNYNkJVTjZVcnJoclZHMy9NbjhEMFJJZnRBUUdwTHNVSmZyNjN1NldvTGpQZVpkaVcvekhmdmppT1VKd2JDcUlmUnAzY0pDc254QStzTldwdi9vdUwiLCJtYWMiOiJjMjJkMDVkMWM4MzNiMTMzNzUyNTU0MjIwN2NlYTJhYTQ2NWFiMGU3NmMzMTNkNTFiMTEzM2MyZDMwMzg1NTE2IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImZtNzVWcE15MmorSXljS2tBaDhEZHc9PSIsInZhbHVlIjoiWDJHOVNpbS9zcldIS1ZEWEt6OXNMVjBhNzR1S3B4T3V4RTZGSkJkWDBsMGpramlMZTBmYitBMmM2T2JJVkluWTNjV3lHdFlTelhCaTJ2Q3V4N1ZFcDJ2VHhzLy9IVmtoLzNqYmdRcUxWOGFxNnpldFQ0dko2alZrcXhWK2FnTW8iLCJtYWMiOiI1ZDliYWY1MmE3NmQ1ZWU2N2MxNDhjODBkZDIwMjMxZGMyZWU2ZGZjMWZkMjJhODVjNmRjMjliNTEzMWQyNjUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116684155\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-611013105 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|IbeS3I2W02eAkiae8I30deZtxSJuBVzomEN4VW4GfzaWyD7XMplnXtrAPk3Q|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o4qeOX4JeNGwBuCfe3fqu5iqZVUYsFaZmtFfdug8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611013105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1020512565 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:32:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">54</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IndodHBJSkxMbmZkZTFqN0VPNkFralE9PSIsInZhbHVlIjoiMytIbXN6S2pRdUlTQ0hWcTkrbHdxUndIZ2ZiY0ZBZ1hQSjF6R1pkYVZ4M1NZc1k2ZnFLVTNNWldpbkpCUjE1SERMaEs1SFUwdi9xa0wwQUpZWXhzUHFveVdhZ1Z4S2tGVTlWc3hZSVFyR2IzK1ZhajBUOFhPZFdFc1YxcElaangiLCJtYWMiOiJhYWY0NDlhZWE0MDAwNzc2MzhmNWEyMTFlMjAzODE2MTczNWU0MzQ2MzA3MDQzOGU5NDI0OWU2ODRkMWViNGZjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:32:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IkEwYk5xS3ZyVGZzdVJkbHhQT2xmL0E9PSIsInZhbHVlIjoiQmJTNHFmcWV0UzNrZmFBNW1zMnZHL0srL1l4L2RTR29paW9NblpLUzFiUmJKU0ZYb2hDWlFzMUpmNnBGUXdnc0krcGR2eHZmWjNUZDJUbVdDNmErTCt3MmNZaFFPdk5ZNkx1WXBvWjZydll1YUN4K3lWMlU1eDFtUE9QUkdydEQiLCJtYWMiOiIwMTAxMzIyYzJhY2UwZmE1YmUzNzJlN2ZkYmQ1MWE3MjhiNzc0NGJhYjJjMjk5OTU5NTZiYzViMGYyZDMzMjE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:32:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IndodHBJSkxMbmZkZTFqN0VPNkFralE9PSIsInZhbHVlIjoiMytIbXN6S2pRdUlTQ0hWcTkrbHdxUndIZ2ZiY0ZBZ1hQSjF6R1pkYVZ4M1NZc1k2ZnFLVTNNWldpbkpCUjE1SERMaEs1SFUwdi9xa0wwQUpZWXhzUHFveVdhZ1Z4S2tGVTlWc3hZSVFyR2IzK1ZhajBUOFhPZFdFc1YxcElaangiLCJtYWMiOiJhYWY0NDlhZWE0MDAwNzc2MzhmNWEyMTFlMjAzODE2MTczNWU0MzQ2MzA3MDQzOGU5NDI0OWU2ODRkMWViNGZjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:32:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IkEwYk5xS3ZyVGZzdVJkbHhQT2xmL0E9PSIsInZhbHVlIjoiQmJTNHFmcWV0UzNrZmFBNW1zMnZHL0srL1l4L2RTR29paW9NblpLUzFiUmJKU0ZYb2hDWlFzMUpmNnBGUXdnc0krcGR2eHZmWjNUZDJUbVdDNmErTCt3MmNZaFFPdk5ZNkx1WXBvWjZydll1YUN4K3lWMlU1eDFtUE9QUkdydEQiLCJtYWMiOiIwMTAxMzIyYzJhY2UwZmE1YmUzNzJlN2ZkYmQ1MWE3MjhiNzc0NGJhYjJjMjk5OTU5NTZiYzViMGYyZDMzMjE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:32:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020512565\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus"}, "badge": null}}