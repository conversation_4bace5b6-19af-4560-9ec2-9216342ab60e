{"__meta": {"id": "01JYR2JVQY4Y9S5TH43CHHVT7Y", "datetime": "2025-06-27 06:53:14", "utime": **********.880047, "method": "GET", "uri": "/api/2024-12/image-quality-score", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751007193.959149, "end": **********.880074, "duration": 0.9209251403808594, "duration_str": "921ms", "measures": [{"label": "Booting", "start": 1751007193.959149, "relative_start": 0, "end": **********.758827, "relative_end": **********.758827, "duration": 0.****************, "duration_str": "800ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.758846, "relative_start": 0.****************, "end": **********.880077, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.782014, "relative_start": 0.****************, "end": **********.791517, "relative_end": **********.791517, "duration": 0.*****************, "duration_str": "9.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.870143, "relative_start": 0.****************, "end": **********.870432, "relative_end": **********.870432, "duration": 0.00028896331787109375, "duration_str": "289μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.875838, "relative_start": 0.****************, "end": **********.875959, "relative_end": **********.875959, "duration": 0.00012087821960449219, "duration_str": "121μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/image-quality-score", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:157-180</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00545, "accumulated_duration_str": "5.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8347812, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 63.67}, {"sql": "select * from `files` where `organization_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8580198, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:159", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=159", "ajax": false, "filename": "DashboardController.php", "line": "159"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 63.67, "width_percent": 36.33}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/image-quality-score\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/image-quality-score", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore", "uri": "GET api/2024-12/image-quality-score", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=157\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:157-180</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f40d02f-6b39-4ffd-b290-98168a5f38df\" target=\"_blank\">View in Telescope</a>", "duration": "925ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-605034783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-605034783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1167555341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167555341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-84585936 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVweFFNeFUyY3NPdjBlUmNKRE82eEE9PSIsInZhbHVlIjoidnRJeFNDUFU4aWlJYTJXdmIzcC95ZjB4M3BVbWNNSWhGUlp3QmxZOG5KUzMvblRnOWl1UEpuNjhEV1pHUWV3ekp2SG0xYS81aExsQzZkbkFycUJTa29iZGJpV05KMnMrT0JHZGJDeXR6NzIwKzMxaFdKQWNOQkRYc2gxdXhhZ20iLCJtYWMiOiI5MjgxODk5ZGM2NDQ1N2QyNmU0ZjYzMzI2NzgwNzU0YzlmOGMwNzdjNDRhYzMyNDY2ZGFlZWIwN2MyOGVlOWExIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJORzV3YXNOSkthSUNCa0JUYWVZclE9PSIsInZhbHVlIjoiZzB1K1FaMUVBTHRvaGQreFNOd1hRTytSSnpTY1lTU1BxRVRpNGpEL00xbkM2ZUNOWFdhbFc5Z1NkSWVtMjdxV1Vtbm5XekxTTzl0RXlwRUhsL1VxWmJybkppQlUxMmRIY0xYaEVoR0t4VUN0V3BNM1NUZGRYMHdsQ1lKdDRRWXo4akE3Z0c3LzhlR1JVY3E5OWFBN1hnPT0iLCJtYWMiOiJhODM4ZGFjODBjZDk4YzBkMjMxYWFiMzdkYWE3MWQ5ZGVhYTEwNjU1MTNhOTFmNDZjNDllN2MxNTQ4YzM1YjJhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVweFFNeFUyY3NPdjBlUmNKRE82eEE9PSIsInZhbHVlIjoidnRJeFNDUFU4aWlJYTJXdmIzcC95ZjB4M3BVbWNNSWhGUlp3QmxZOG5KUzMvblRnOWl1UEpuNjhEV1pHUWV3ekp2SG0xYS81aExsQzZkbkFycUJTa29iZGJpV05KMnMrT0JHZGJDeXR6NzIwKzMxaFdKQWNOQkRYc2gxdXhhZ20iLCJtYWMiOiI5MjgxODk5ZGM2NDQ1N2QyNmU0ZjYzMzI2NzgwNzU0YzlmOGMwNzdjNDRhYzMyNDY2ZGFlZWIwN2MyOGVlOWExIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImtOT0xkalRQR3B0UFB0N3ByQjFnNGc9PSIsInZhbHVlIjoiZ3NQMTR6NVR0c1d1N1ZycHNrc0tWTFZQSWlqajJHNVEwVkwzT2g5cE01dmZRWHJlUDVCZjlYcDJyeFlzQ1BZTEdDK1B0MElNZmtGU3Rab0RLNzNKc1pVN3A2dm5vQ1ljYVVHbFBsY1AzbGlETVNZNW5VRGtuNkM5eGE5VDRTa2kiLCJtYWMiOiIwNDRjOTFmYzQ3NzBjODZiZTFjMTU3MTliNjk1ZWI1MzdhOGQ5YWFlNGFhZTA2OGVlYjkyZDFjNGRjZTUzMWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84585936\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-94837110 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2mWd79PpeLkLTlAr4CMlCbKJn5gEwhRI3ndPAwIe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94837110\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1375975372 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 06:53:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjMxME81WW5tU0g1akFGN29ublFWdEE9PSIsInZhbHVlIjoiNFJuVXJ5bGoxemxzRVZWc1UxQVltQ2k5YlZmc05pbEhvMG1mRGxrVkxuL09yUVRrNUN5eFpyYnlCbEJQTTM1Nm1Va0lVZ21DaUMxaVZqVUkzazlrTGxsL1ZZMFJ0eURPdCszUjJkY2hXenBnWWUyaXQwS3drS29kMjhlcXNZSUYiLCJtYWMiOiJjMzhhMjViMDUwYjliNDFkYzFjZjU2ZjhlMDUzZmE3ZWQ3MTQ0NWM2ZGRiYTA2ZDAxOGJkYjE2MjI2YWM0NjM3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 08:53:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6InZraU9iQm5pWDFNTzFkcW5YUHNBS0E9PSIsInZhbHVlIjoiQmNnTmo4V2ZucDAxSHVQNUVGYncwWFE3dmdEbUQrbGMwYy93bFF2OHNpV1A3Y1FZVmE0L0doWUU5VjVhbFpPcTVkai8xdFVlck03aDVRcFFKd1JIL3JDMGJIdG9vSHVPUEJoQVJOMUZkVUlKTVRjTE9uTjlvZThwcXJKQ3ZDVEIiLCJtYWMiOiJjNTE2ZWMzMmVkY2M4MTI0ZGU3YmVmYjA3YjYzYjMwYTFmYWJhMjg1OGQwZmE3OTEzNWI4YjIzMzZhYWU2NGU1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 08:53:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjMxME81WW5tU0g1akFGN29ublFWdEE9PSIsInZhbHVlIjoiNFJuVXJ5bGoxemxzRVZWc1UxQVltQ2k5YlZmc05pbEhvMG1mRGxrVkxuL09yUVRrNUN5eFpyYnlCbEJQTTM1Nm1Va0lVZ21DaUMxaVZqVUkzazlrTGxsL1ZZMFJ0eURPdCszUjJkY2hXenBnWWUyaXQwS3drS29kMjhlcXNZSUYiLCJtYWMiOiJjMzhhMjViMDUwYjliNDFkYzFjZjU2ZjhlMDUzZmE3ZWQ3MTQ0NWM2ZGRiYTA2ZDAxOGJkYjE2MjI2YWM0NjM3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 08:53:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6InZraU9iQm5pWDFNTzFkcW5YUHNBS0E9PSIsInZhbHVlIjoiQmNnTmo4V2ZucDAxSHVQNUVGYncwWFE3dmdEbUQrbGMwYy93bFF2OHNpV1A3Y1FZVmE0L0doWUU5VjVhbFpPcTVkai8xdFVlck03aDVRcFFKd1JIL3JDMGJIdG9vSHVPUEJoQVJOMUZkVUlKTVRjTE9uTjlvZThwcXJKQ3ZDVEIiLCJtYWMiOiJjNTE2ZWMzMmVkY2M4MTI0ZGU3YmVmYjA3YjYzYjMwYTFmYWJhMjg1OGQwZmE3OTEzNWI4YjIzMzZhYWU2NGU1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 08:53:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375975372\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1289319154 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MMjlbzFJCZAHYLPSD0tkt1bisHPRzPqVvC7ztng1</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/image-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289319154\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/image-quality-score", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ImageQualityScore"}, "badge": null}}