import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchOrganizationBillingStatus,
    selectBillingStatus,
    selectOrganizationLoading,
    selectOrganizationError,
} from "../store/slices/organizationSlice";

const ClockIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto">
        <path
            d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
            stroke="#DC3545"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
            stroke="#DC3545"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

const BillingTimer = ({ variant = "main" }) => {
    const dispatch = useDispatch();
    const billingStatus = useSelector(selectBillingStatus);
    const loading = useSelector(selectOrganizationLoading);
    const error = useSelector(selectOrganizationError);

    useEffect(() => {
        // Always fetch billing status - backend handles authentication
        console.log("BillingTimer: Fetching organization billing status...");
        dispatch(fetchOrganizationBillingStatus());
    }, [dispatch]);

    // Debug: Always log the current state

    if (error) {
        console.error("BillingTimer: Error:", error);
        return (
            <div className="mt-5 billing-timer">
                <div className="position-relative">
                    <div
                        className="billing-custom-css billing-custom-css_main billing-custom-css-main-menu mx-auto d-none d-lg-block"
                        style={{ zIndex: 1000, position: "absolute" }}
                    >
                        <div className="mb-3 text-center">
                            <p>Error loading billing status: {error}</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!billingStatus) {
        console.log("BillingTimer: No billing status data");
        return (
            <div className="mt-5 billing-timer">
                <div className="position-relative">
                    <div
                        className="billing-custom-css billing-custom-css_main billing-custom-css-main-menu mx-auto d-none d-lg-block"
                        style={{ zIndex: 1000, position: "absolute" }}
                    >
                        <div className="mb-3 text-center">
                            <p>No billing status data</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Don't render if user is subscribed and subscription is not canceled
    if (billingStatus.is_subscribed && !billingStatus.subscription_canceled) {
        return null;
    }

    // Show trial status if we have billing data and user is on trial
    if (billingStatus && billingStatus.on_trial) {
        return (
            <div className="bg-white">
                <div className="mt-5  rounded-lg m-4 p-4" style={{ background: "#fff3f4" }}>
                    <div className="position-relative">
                        <div
                            className="billing-custom-css billing-custom-css_main billing-custom-css-main-menu mx-auto"
                            style={{ zIndex: 1000, position: "relative" }}
                        >
                            <div className="mb-3 text-center">
                                <ClockIcon />
                            </div>
                            <h3 className="text-center">
                                <b>Trial Plan</b>
                            </h3>
                            <p className="mb-3 text-center">
                                {billingStatus.days_left_in_trial !== null && billingStatus.days_left_in_trial > 0
                                    ? `${billingStatus.days_left_in_trial} Days left in your trial.`
                                    : "Trial expired."}
                            </p>
                            <div className="text-center">
                                <a href="/billing" className="p-2 rounded-md text-white bg-[#DC3545]">
                                    Click to Upgrade
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Show subscription canceled status
    if (billingStatus && billingStatus.is_subscribed && billingStatus.subscription_canceled) {
        return (
            <div className="bg-white">
                <div className="mt-5  rounded-lg m-4 p-4" style={{ background: "#fff3f4" }}>
                    <div className="position-relative">
                        <div
                            className="billing-custom-css billing-custom-css_main billing-custom-css-main-menu mx-auto"
                            style={{ zIndex: 1000, position: "relative" }}
                        >
                            <div className="mb-3 text-center">
                                <ClockIcon />
                            </div>
                            <h3 className="text-center">
                                <b>Subscription canceled</b>
                            </h3>
                            <p className="mb-3 text-center">
                                {billingStatus.days_left_in_grace_period !== null && billingStatus.days_left_in_grace_period > 0
                                    ? `${billingStatus.days_left_in_grace_period} Days left in your plan.`
                                    : "Grace period expired."}
                            </p>
                            <div className="text-center">
                                <a href="/billing" className="p-2 rounded-md text-white bg-[#DC3545]">
                                    Click to Resume
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Show trial expired status
    if (billingStatus && !billingStatus.on_trial && !billingStatus.is_subscribed) {
        return (
            <div className="bg-white">
                <div className="mt-5  rounded-lg m-4 p-4" style={{ background: "#fff3f4" }}>
                    <div className="position-relative">
                        <div
                            className="billing-custom-css billing-custom-css_main billing-custom-css-main-menu mx-auto"
                            style={{ zIndex: 1000, position: "relative" }}
                        >
                            <div className="mb-3 text-center">
                                <ClockIcon />
                            </div>
                            <h3 className="text-center">
                                <b>Trial Plan</b>
                            </h3>
                            <p className="mb-3 text-center">Trial expired.</p>
                            <div className="text-center">
                                <a href="/billing" className="p-2 rounded-md text-white bg-[#DC3545]">
                                    Click to Upgrade
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
};

export default BillingTimer;
