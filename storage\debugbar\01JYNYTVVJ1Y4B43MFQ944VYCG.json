{"__meta": {"id": "01JYNYTVVJ1Y4B43MFQ944VYCG", "datetime": "2025-06-26 11:09:13", "utime": **********.971681, "method": "POST", "uri": "/products/export", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750936151.556817, "end": **********.971891, "duration": 2.415073871612549, "duration_str": "2.42s", "measures": [{"label": "Booting", "start": 1750936151.556817, "relative_start": 0, "end": **********.722424, "relative_end": **********.722424, "duration": 1.**************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.722449, "relative_start": 1.****************, "end": **********.971897, "relative_end": 5.9604644775390625e-06, "duration": 1.****************, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.750549, "relative_start": 1.****************, "end": **********.762073, "relative_end": **********.762073, "duration": 0.011523962020874023, "duration_str": "11.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.965155, "relative_start": 2.****************, "end": **********.966351, "relative_end": **********.966351, "duration": 0.001196146011352539, "duration_str": "1.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST products/export", "middleware": "web, check_billing, prime.user, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ExportController@export_csv<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FExportController.php&line=210\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "as": "products.export", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FExportController.php&line=210\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ExportController.php:210-317</a>"}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06286, "accumulated_duration_str": "62.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.805932, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 9.481}, {"sql": "select * from `organizations` where `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 87}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.845941, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.481, "width_percent": 4.613}, {"sql": "select * from `organizations` where `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AuthServiceProvider.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 406}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 354}], "start": **********.854565, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "AuthServiceProvider.php:69", "source": {"index": 20, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AuthServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FProviders%2FAuthServiceProvider.php&line=69", "ajax": false, "filename": "AuthServiceProvider.php", "line": "69"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.095, "width_percent": 4.025}, {"sql": "select * from `versions` where `versions`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ExportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ExportController.php", "line": 234}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.879745, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ExportController.php:234", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ExportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ExportController.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FExportController.php&line=234", "ajax": false, "filename": "ExportController.php", "line": "234"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.12, "width_percent": 1.018}, {"sql": "select * from `templates` where `organization_id` = '1' and (json_contains(`version_id`, '\\\"1\\\"')) and (json_contains(`channel_id`, '\\\"1\\\"')) and `name` = '5555' and `type` = 'export' and `export_type` = 'custom' and `id` not in (2) and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "\"1\"", "\"1\"", "5555", "export", "custom", 2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/Http/Controllers/MappingFieldController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\Http\\Controllers\\MappingFieldController.php", "line": 270}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ExportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ExportController.php", "line": 263}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.892659, "duration": 0.00734, "duration_str": "7.34ms", "memory": 0, "memory_str": null, "filename": "MappingFieldController.php:270", "source": {"index": 15, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/Http/Controllers/MappingFieldController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\Http\\Controllers\\MappingFieldController.php", "line": 270}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2FHttp%2FControllers%2FMappingFieldController.php&line=270", "ajax": false, "filename": "MappingFieldController.php", "line": "270"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.138, "width_percent": 11.677}, {"sql": "select * from `templates` where `templates`.`id` = 2 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 90}, {"index": 20, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/Http/Controllers/MappingFieldController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\Http\\Controllers\\MappingFieldController.php", "line": 291}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ExportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ExportController.php", "line": 263}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9073198, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Template.php:90", "source": {"index": 19, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=90", "ajax": false, "filename": "Template.php", "line": "90"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.815, "width_percent": 1.257}, {"sql": "update `templates` set `payload` = '{\\\"data\\\":[{\\\"from\\\":[\\\"Default,handle\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Product Identifier\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Default,file\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Product Images\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Default,vendor\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Vendor\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Default,brand\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Brand\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Default,categories\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Category\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Default,status\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Product Status\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"General,product_name\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Product Name\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"General,description\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Description\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,sku\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SKU\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,name\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Name\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,file\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Image\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,price\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Price\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,compare_at_price\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Compare at Price\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,cost_price\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Cost Price\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,barcode\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"UPC \\\\/ Barcode\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,weight\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Weight\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,weight_unit\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Weight Unit\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,track_quantity\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Track Quantity\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Variant,continue_selling\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Continue Selling\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"Inventory -> Tanzayb Store,1\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Tanzayb Store Warehouse\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"SEO,seo_url\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"URL Slug\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"SEO,seo_title\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SEO Title\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"SEO,seo_description\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SEO Description\\\"],\\\"with\\\":null,\\\"replace\\\":null},{\\\"from\\\":[\\\"SEO,seo_keyword\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Tags\\\"],\\\"with\\\":null,\\\"replace\\\":null}]}', `templates`.`updated_at` = '2025-06-26 11:09:12' where `id` = 2", "type": "query", "params": [], "bindings": ["{\"data\":[{\"from\":[\"Default,handle\"],\"with_formula\":\"assign\",\"to\":[\"Product Identifier\"],\"with\":null,\"replace\":null},{\"from\":[\"Default,file\"],\"with_formula\":\"assign\",\"to\":[\"Product Images\"],\"with\":null,\"replace\":null},{\"from\":[\"Default,vendor\"],\"with_formula\":\"assign\",\"to\":[\"Vendor\"],\"with\":null,\"replace\":null},{\"from\":[\"Default,brand\"],\"with_formula\":\"assign\",\"to\":[\"Brand\"],\"with\":null,\"replace\":null},{\"from\":[\"Default,categories\"],\"with_formula\":\"assign\",\"to\":[\"Category\"],\"with\":null,\"replace\":null},{\"from\":[\"Default,status\"],\"with_formula\":\"assign\",\"to\":[\"Product Status\"],\"with\":null,\"replace\":null},{\"from\":[\"General,product_name\"],\"with_formula\":\"assign\",\"to\":[\"Product Name\"],\"with\":null,\"replace\":null},{\"from\":[\"General,description\"],\"with_formula\":\"assign\",\"to\":[\"Description\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,sku\"],\"with_formula\":\"assign\",\"to\":[\"SKU\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,name\"],\"with_formula\":\"assign\",\"to\":[\"Name\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,file\"],\"with_formula\":\"assign\",\"to\":[\"Image\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,price\"],\"with_formula\":\"assign\",\"to\":[\"Price\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,compare_at_price\"],\"with_formula\":\"assign\",\"to\":[\"Compare at Price\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,cost_price\"],\"with_formula\":\"assign\",\"to\":[\"Cost Price\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,barcode\"],\"with_formula\":\"assign\",\"to\":[\"UPC \\/ Barcode\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,weight\"],\"with_formula\":\"assign\",\"to\":[\"Weight\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,weight_unit\"],\"with_formula\":\"assign\",\"to\":[\"Weight Unit\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,track_quantity\"],\"with_formula\":\"assign\",\"to\":[\"Track Quantity\"],\"with\":null,\"replace\":null},{\"from\":[\"Variant,continue_selling\"],\"with_formula\":\"assign\",\"to\":[\"Continue Selling\"],\"with\":null,\"replace\":null},{\"from\":[\"Inventory -> Tanzayb Store,1\"],\"with_formula\":\"assign\",\"to\":[\"Tanzayb Store Warehouse\"],\"with\":null,\"replace\":null},{\"from\":[\"SEO,seo_url\"],\"with_formula\":\"assign\",\"to\":[\"URL Slug\"],\"with\":null,\"replace\":null},{\"from\":[\"SEO,seo_title\"],\"with_formula\":\"assign\",\"to\":[\"SEO Title\"],\"with\":null,\"replace\":null},{\"from\":[\"SEO,seo_description\"],\"with_formula\":\"assign\",\"to\":[\"SEO Description\"],\"with\":null,\"replace\":null},{\"from\":[\"SEO,seo_keyword\"],\"with_formula\":\"assign\",\"to\":[\"Tags\"],\"with\":null,\"replace\":null}]}", "2025-06-26 11:09:12", 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 108}, {"index": 15, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/Http/Controllers/MappingFieldController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\Http\\Controllers\\MappingFieldController.php", "line": 291}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ExportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ExportController.php", "line": 263}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.912189, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "Template.php:108", "source": {"index": 14, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=108", "ajax": false, "filename": "Template.php", "line": "108"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.071, "width_percent": 10.038}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"1fa6679c-a09a-4888-935b-9d57f99dd9c1\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\ExportProducts\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\ExportProducts\\\",\\\"command\\\":\\\"O:23:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\ExportProducts\\\\\\\":1:{s:4:\\\\\\\"data\\\\\\\";a:10:{s:9:\\\\\\\"file_path\\\\\\\";N;s:11:\\\\\\\"input_array\\\\\\\";a:2:{s:10:\\\\\\\"array_name\\\\\\\";s:6:\\\\\\\"Apimio\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:5:{i:0;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"Default\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:6:{s:6:\\\\\\\"handle\\\\\\\";s:18:\\\\\\\"Product Identifier\\\\\\\";s:4:\\\\\\\"file\\\\\\\";s:14:\\\\\\\"Product Images\\\\\\\";s:6:\\\\\\\"vendor\\\\\\\";s:6:\\\\\\\"Vendor\\\\\\\";s:5:\\\\\\\"brand\\\\\\\";s:5:\\\\\\\"Brand\\\\\\\";s:10:\\\\\\\"categories\\\\\\\";s:8:\\\\\\\"Category\\\\\\\";s:6:\\\\\\\"status\\\\\\\";s:14:\\\\\\\"Product Status\\\\\\\";}}i:1;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"General\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:2:{s:12:\\\\\\\"product_name\\\\\\\";s:12:\\\\\\\"Product Name\\\\\\\";s:11:\\\\\\\"description\\\\\\\";s:11:\\\\\\\"Description\\\\\\\";}}i:2;a:2:{s:4:\\\\\\\"name\\\\\\\";s:7:\\\\\\\"Variant\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:11:{s:3:\\\\\\\"sku\\\\\\\";s:3:\\\\\\\"SKU\\\\\\\";s:4:\\\\\\\"name\\\\\\\";s:4:\\\\\\\"Name\\\\\\\";s:4:\\\\\\\"file\\\\\\\";s:5:\\\\\\\"Image\\\\\\\";s:5:\\\\\\\"price\\\\\\\";s:5:\\\\\\\"Price\\\\\\\";s:16:\\\\\\\"compare_at_price\\\\\\\";s:16:\\\\\\\"Compare at Price\\\\\\\";s:10:\\\\\\\"cost_price\\\\\\\";s:10:\\\\\\\"Cost Price\\\\\\\";s:7:\\\\\\\"barcode\\\\\\\";s:13:\\\\\\\"UPC \\\\/ Barcode\\\\\\\";s:6:\\\\\\\"weight\\\\\\\";s:6:\\\\\\\"Weight\\\\\\\";s:11:\\\\\\\"weight_unit\\\\\\\";s:11:\\\\\\\"Weight Unit\\\\\\\";s:14:\\\\\\\"track_quantity\\\\\\\";s:14:\\\\\\\"Track Quantity\\\\\\\";s:16:\\\\\\\"continue_selling\\\\\\\";s:16:\\\\\\\"Continue Selling\\\\\\\";}}i:3;a:2:{s:4:\\\\\\\"name\\\\\\\";s:26:\\\\\\\"Inventory -> Tanzayb Store\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:1:{i:1;s:23:\\\\\\\"Tanzayb Store Warehouse\\\\\\\";}}i:4;a:2:{s:4:\\\\\\\"name\\\\\\\";s:3:\\\\\\\"SEO\\\\\\\";s:10:\\\\\\\"attributes\\\\\\\";a:4:{s:7:\\\\\\\"seo_url\\\\\\\";s:8:\\\\\\\"URL Slug\\\\\\\";s:9:\\\\\\\"seo_title\\\\\\\";s:9:\\\\\\\"SEO Title\\\\\\\";s:15:\\\\\\\"seo_description\\\\\\\";s:15:\\\\\\\"SEO Description\\\\\\\";s:11:\\\\\\\"seo_keyword\\\\\\\";s:4:\\\\\\\"Tags\\\\\\\";}}}}s:12:\\\\\\\"output_array\\\\\\\";a:2:{s:10:\\\\\\\"array_name\\\\\\\";s:6:\\\\\\\"Export\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:0:{}}s:13:\\\\\\\"data_required\\\\\\\";a:10:{s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:11:\\\\\\\"output_type\\\\\\\";s:6:\\\\\\\"export\\\\\\\";s:20:\\\\\\\"template_method_type\\\\\\\";s:6:\\\\\\\"export\\\\\\\";s:19:\\\\\\\"apimio_plans_access\\\\\\\";a:2:{s:10:\\\\\\\"prime_user\\\\\\\";b:1;s:13:\\\\\\\"template_save\\\\\\\";b:1;}s:4:\\\\\\\"sync\\\\\\\";b:0;s:18:\\\\\\\"redirect_url_route\\\\\\\";s:15:\\\\\\\"products.export\\\\\\\";s:8:\\\\\\\"versions\\\\\\\";a:1:{i:1;s:5:\\\\\\\"EN-US\\\\\\\";}s:8:\\\\\\\"catalogs\\\\\\\";a:1:{i:1;s:13:\\\\\\\"Tanzayb Store\\\\\\\";}s:12:\\\\\\\"filter_query\\\\\\\";a:2:{s:24:\\\\\\\"filter_export_productIds\\\\\\\";s:3:\\\\\\\"all\\\\\\\";s:26:\\\\\\\"filter_total_product_array\\\\\\\";N;}s:11:\\\\\\\"export_type\\\\\\\";s:6:\\\\\\\"custom\\\\\\\";}s:19:\\\\\\\"template_attributes\\\\\\\";a:0:{}s:12:\\\\\\\"request_data\\\\\\\";a:14:{s:6:\\\\\\\"_token\\\\\\\";s:40:\\\\\\\"8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j\\\\\\\";s:9:\\\\\\\"file_path\\\\\\\";N;s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:20:\\\\\\\"template_method_type\\\\\\\";s:6:\\\\\\\"export\\\\\\\";s:5:\\\\\\\"nodes\\\\\\\";a:1:{s:4:\\\\\\\"data\\\\\\\";a:24:{i:0;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Default,handle\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:18:\\\\\\\"Product Identifier\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:1;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:12:\\\\\\\"Default,file\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Product Images\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:2;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Default,vendor\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:6:\\\\\\\"Vendor\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:3;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Default,brand\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:5:\\\\\\\"Brand\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:4;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:18:\\\\\\\"Default,categories\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:8:\\\\\\\"Category\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:5;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Default,status\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Product Status\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:6;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:20:\\\\\\\"General,product_name\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:12:\\\\\\\"Product Name\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:7;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:19:\\\\\\\"General,description\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Description\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:8;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Variant,sku\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:3:\\\\\\\"SKU\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:9;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:12:\\\\\\\"Variant,name\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:4:\\\\\\\"Name\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:10;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:12:\\\\\\\"Variant,file\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:5:\\\\\\\"Image\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:11;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:13:\\\\\\\"Variant,price\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:5:\\\\\\\"Price\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:12;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:24:\\\\\\\"Variant,compare_at_price\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:16:\\\\\\\"Compare at Price\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:13;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:18:\\\\\\\"Variant,cost_price\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:10:\\\\\\\"Cost Price\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:14;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:15:\\\\\\\"Variant,barcode\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:13:\\\\\\\"UPC \\\\/ Barcode\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:15;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Variant,weight\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:6:\\\\\\\"Weight\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:16;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:19:\\\\\\\"Variant,weight_unit\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:11:\\\\\\\"Weight Unit\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:17;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:22:\\\\\\\"Variant,track_quantity\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:14:\\\\\\\"Track Quantity\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:18;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:24:\\\\\\\"Variant,continue_selling\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:16:\\\\\\\"Continue Selling\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:19;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:28:\\\\\\\"Inventory -> Tanzayb Store,1\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:23:\\\\\\\"Tanzayb Store Warehouse\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:20;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:11:\\\\\\\"SEO,seo_url\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:8:\\\\\\\"URL Slug\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:21;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:13:\\\\\\\"SEO,seo_title\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:9:\\\\\\\"SEO Title\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:22;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:19:\\\\\\\"SEO,seo_description\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:15:\\\\\\\"SEO Description\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}i:23;a:5:{s:4:\\\\\\\"from\\\\\\\";a:1:{i:0;s:15:\\\\\\\"SEO,seo_keyword\\\\\\\";}s:12:\\\\\\\"with_formula\\\\\\\";s:6:\\\\\\\"assign\\\\\\\";s:2:\\\\\\\"to\\\\\\\";a:1:{i:0;s:4:\\\\\\\"Tags\\\\\\\";}s:4:\\\\\\\"with\\\\\\\";N;s:7:\\\\\\\"replace\\\\\\\";N;}}}s:7:\\\\\\\"version\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:7:\\\\\\\"catalog\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:15:\\\\\\\"ignore_unmapped\\\\\\\";s:3:\\\\\\\"off\\\\\\\";s:9:\\\\\\\"temp_name\\\\\\\";s:4:\\\\\\\"5555\\\\\\\";s:7:\\\\\\\"temp_id\\\\\\\";i:2;s:6:\\\\\\\"status\\\\\\\";s:1:\\\\\\\"1\\\\\\\";s:11:\\\\\\\"export_type\\\\\\\";s:6:\\\\\\\"custom\\\\\\\";s:13:\\\\\\\"import_action\\\\\\\";N;s:11:\\\\\\\"temp_status\\\\\\\";s:2:\\\\\\\"on\\\\\\\";}s:12:\\\\\\\"version_name\\\\\\\";s:5:\\\\\\\"EN-US\\\\\\\";s:8:\\\\\\\"filename\\\\\\\";s:55:\\\\\\\"temp_files\\\\/custom_en_us_products_2025_06_26_110912.xlsx\\\\\\\";s:4:\\\\\\\"user\\\\\\\";O:8:\\\\\\\"App\\\\\\\\User\\\\\\\":32:{s:13:\\\\\\\"\\\\u0000*\\\\u0000connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:8:\\\\\\\"\\\\u0000*\\\\u0000table\\\\\\\";s:5:\\\\\\\"users\\\\\\\";s:13:\\\\\\\"\\\\u0000*\\\\u0000primaryKey\\\\\\\";s:2:\\\\\\\"id\\\\\\\";s:10:\\\\\\\"\\\\u0000*\\\\u0000keyType\\\\\\\";s:3:\\\\\\\"int\\\\\\\";s:12:\\\\\\\"incrementing\\\\\\\";b:1;s:7:\\\\\\\"\\\\u0000*\\\\u0000with\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000withCount\\\\\\\";a:0:{}s:19:\\\\\\\"preventsLazyLoading\\\\\\\";b:0;s:10:\\\\\\\"\\\\u0000*\\\\u0000perPage\\\\\\\";i:15;s:6:\\\\\\\"exists\\\\\\\";b:1;s:18:\\\\\\\"wasRecentlyCreated\\\\\\\";b:0;s:28:\\\\\\\"\\\\u0000*\\\\u0000escapeWhenCastingToString\\\\\\\";b:0;s:13:\\\\\\\"\\\\u0000*\\\\u0000attributes\\\\\\\";a:16:{s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"google_id\\\\\\\";s:21:\\\\\\\"111325706919938389600\\\\\\\";s:15:\\\\\\\"shopify_shop_id\\\\\\\";N;s:13:\\\\\\\"freshworks_id\\\\\\\";N;s:5:\\\\\\\"fname\\\\\\\";s:12:\\\\\\\"Bilal Arshad\\\\\\\";s:5:\\\\\\\"lname\\\\\\\";N;s:5:\\\\\\\"email\\\\\\\";s:23:\\\\\\\"<EMAIL>\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";N;s:17:\\\\\\\"email_verified_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:8:\\\\\\\"password\\\\\\\";N;s:5:\\\\\\\"phone\\\\\\\";N;s:10:\\\\\\\"last_login\\\\\\\";s:19:\\\\\\\"2025-06-26 07:35:07\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:60:\\\\\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\\\\\";s:12:\\\\\\\"block_status\\\\\\\";i:0;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-26 07:35:07\\\\\\\";}s:11:\\\\\\\"\\\\u0000*\\\\u0000original\\\\\\\";a:16:{s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"google_id\\\\\\\";s:21:\\\\\\\"111325706919938389600\\\\\\\";s:15:\\\\\\\"shopify_shop_id\\\\\\\";N;s:13:\\\\\\\"freshworks_id\\\\\\\";N;s:5:\\\\\\\"fname\\\\\\\";s:12:\\\\\\\"Bilal Arshad\\\\\\\";s:5:\\\\\\\"lname\\\\\\\";N;s:5:\\\\\\\"email\\\\\\\";s:23:\\\\\\\"<EMAIL>\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";N;s:17:\\\\\\\"email_verified_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:8:\\\\\\\"password\\\\\\\";N;s:5:\\\\\\\"phone\\\\\\\";N;s:10:\\\\\\\"last_login\\\\\\\";s:19:\\\\\\\"2025-06-26 07:35:07\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:60:\\\\\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\\\\\";s:12:\\\\\\\"block_status\\\\\\\";i:0;s:10:\\\\\\\"created_at\\\\\\\";s:19:\\\\\\\"2025-06-23 08:21:56\\\\\\\";s:10:\\\\\\\"updated_at\\\\\\\";s:19:\\\\\\\"2025-06-26 07:35:07\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000changes\\\\\\\";a:0:{}s:8:\\\\\\\"\\\\u0000*\\\\u0000casts\\\\\\\";a:1:{s:17:\\\\\\\"email_verified_at\\\\\\\";s:8:\\\\\\\"datetime\\\\\\\";}s:17:\\\\\\\"\\\\u0000*\\\\u0000classCastCache\\\\\\\";a:0:{}s:21:\\\\\\\"\\\\u0000*\\\\u0000attributeCastCache\\\\\\\";a:0:{}s:13:\\\\\\\"\\\\u0000*\\\\u0000dateFormat\\\\\\\";N;s:10:\\\\\\\"\\\\u0000*\\\\u0000appends\\\\\\\";a:0:{}s:19:\\\\\\\"\\\\u0000*\\\\u0000dispatchesEvents\\\\\\\";a:0:{}s:14:\\\\\\\"\\\\u0000*\\\\u0000observables\\\\\\\";a:0:{}s:12:\\\\\\\"\\\\u0000*\\\\u0000relations\\\\\\\";a:0:{}s:10:\\\\\\\"\\\\u0000*\\\\u0000touches\\\\\\\";a:0:{}s:10:\\\\\\\"timestamps\\\\\\\";b:1;s:13:\\\\\\\"usesUniqueIds\\\\\\\";b:0;s:9:\\\\\\\"\\\\u0000*\\\\u0000hidden\\\\\\\";a:2:{i:0;s:8:\\\\\\\"password\\\\\\\";i:1;s:14:\\\\\\\"remember_token\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000visible\\\\\\\";a:0:{}s:11:\\\\\\\"\\\\u0000*\\\\u0000fillable\\\\\\\";a:9:{i:0;s:5:\\\\\\\"fname\\\\\\\";i:1;s:5:\\\\\\\"lname\\\\\\\";i:2;s:5:\\\\\\\"email\\\\\\\";i:3;s:8:\\\\\\\"password\\\\\\\";i:4;s:17:\\\\\\\"verification_code\\\\\\\";i:5;s:5:\\\\\\\"phone\\\\\\\";i:6;s:17:\\\\\\\"email_verified_at\\\\\\\";i:7;s:15:\\\\\\\"shopify_shop_id\\\\\\\";i:8;s:10:\\\\\\\"last_login\\\\\\\";}s:10:\\\\\\\"\\\\u0000*\\\\u0000guarded\\\\\\\";a:1:{i:0;s:1:\\\\\\\"*\\\\\\\";}s:20:\\\\\\\"\\\\u0000*\\\\u0000rememberTokenName\\\\\\\";s:14:\\\\\\\"remember_token\\\\\\\";s:14:\\\\\\\"\\\\u0000*\\\\u0000accessToken\\\\\\\";N;}s:15:\\\\\\\"organization_id\\\\\\\";s:1:\\\\\\\"1\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"1fa6679c-a09a-4888-935b-9d57f99dd9c1\",\"displayName\":\"App\\\\Jobs\\\\ExportProducts\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ExportProducts\",\"command\":\"O:23:\\\"App\\\\Jobs\\\\ExportProducts\\\":1:{s:4:\\\"data\\\";a:10:{s:9:\\\"file_path\\\";N;s:11:\\\"input_array\\\";a:2:{s:10:\\\"array_name\\\";s:6:\\\"Apimio\\\";s:5:\\\"nodes\\\";a:5:{i:0;a:2:{s:4:\\\"name\\\";s:7:\\\"Default\\\";s:10:\\\"attributes\\\";a:6:{s:6:\\\"handle\\\";s:18:\\\"Product Identifier\\\";s:4:\\\"file\\\";s:14:\\\"Product Images\\\";s:6:\\\"vendor\\\";s:6:\\\"Vendor\\\";s:5:\\\"brand\\\";s:5:\\\"Brand\\\";s:10:\\\"categories\\\";s:8:\\\"Category\\\";s:6:\\\"status\\\";s:14:\\\"Product Status\\\";}}i:1;a:2:{s:4:\\\"name\\\";s:7:\\\"General\\\";s:10:\\\"attributes\\\";a:2:{s:12:\\\"product_name\\\";s:12:\\\"Product Name\\\";s:11:\\\"description\\\";s:11:\\\"Description\\\";}}i:2;a:2:{s:4:\\\"name\\\";s:7:\\\"Variant\\\";s:10:\\\"attributes\\\";a:11:{s:3:\\\"sku\\\";s:3:\\\"SKU\\\";s:4:\\\"name\\\";s:4:\\\"Name\\\";s:4:\\\"file\\\";s:5:\\\"Image\\\";s:5:\\\"price\\\";s:5:\\\"Price\\\";s:16:\\\"compare_at_price\\\";s:16:\\\"Compare at Price\\\";s:10:\\\"cost_price\\\";s:10:\\\"Cost Price\\\";s:7:\\\"barcode\\\";s:13:\\\"UPC \\/ Barcode\\\";s:6:\\\"weight\\\";s:6:\\\"Weight\\\";s:11:\\\"weight_unit\\\";s:11:\\\"Weight Unit\\\";s:14:\\\"track_quantity\\\";s:14:\\\"Track Quantity\\\";s:16:\\\"continue_selling\\\";s:16:\\\"Continue Selling\\\";}}i:3;a:2:{s:4:\\\"name\\\";s:26:\\\"Inventory -> Tanzayb Store\\\";s:10:\\\"attributes\\\";a:1:{i:1;s:23:\\\"Tanzayb Store Warehouse\\\";}}i:4;a:2:{s:4:\\\"name\\\";s:3:\\\"SEO\\\";s:10:\\\"attributes\\\";a:4:{s:7:\\\"seo_url\\\";s:8:\\\"URL Slug\\\";s:9:\\\"seo_title\\\";s:9:\\\"SEO Title\\\";s:15:\\\"seo_description\\\";s:15:\\\"SEO Description\\\";s:11:\\\"seo_keyword\\\";s:4:\\\"Tags\\\";}}}}s:12:\\\"output_array\\\";a:2:{s:10:\\\"array_name\\\";s:6:\\\"Export\\\";s:5:\\\"nodes\\\";a:0:{}}s:13:\\\"data_required\\\";a:10:{s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:11:\\\"output_type\\\";s:6:\\\"export\\\";s:20:\\\"template_method_type\\\";s:6:\\\"export\\\";s:19:\\\"apimio_plans_access\\\";a:2:{s:10:\\\"prime_user\\\";b:1;s:13:\\\"template_save\\\";b:1;}s:4:\\\"sync\\\";b:0;s:18:\\\"redirect_url_route\\\";s:15:\\\"products.export\\\";s:8:\\\"versions\\\";a:1:{i:1;s:5:\\\"EN-US\\\";}s:8:\\\"catalogs\\\";a:1:{i:1;s:13:\\\"Tanzayb Store\\\";}s:12:\\\"filter_query\\\";a:2:{s:24:\\\"filter_export_productIds\\\";s:3:\\\"all\\\";s:26:\\\"filter_total_product_array\\\";N;}s:11:\\\"export_type\\\";s:6:\\\"custom\\\";}s:19:\\\"template_attributes\\\";a:0:{}s:12:\\\"request_data\\\";a:14:{s:6:\\\"_token\\\";s:40:\\\"8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j\\\";s:9:\\\"file_path\\\";N;s:15:\\\"organization_id\\\";s:1:\\\"1\\\";s:20:\\\"template_method_type\\\";s:6:\\\"export\\\";s:5:\\\"nodes\\\";a:1:{s:4:\\\"data\\\";a:24:{i:0;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:14:\\\"Default,handle\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:18:\\\"Product Identifier\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:1;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:12:\\\"Default,file\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:14:\\\"Product Images\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:2;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:14:\\\"Default,vendor\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:6:\\\"Vendor\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:3;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Default,brand\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:5:\\\"Brand\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:4;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:18:\\\"Default,categories\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:8:\\\"Category\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:5;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:14:\\\"Default,status\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:14:\\\"Product Status\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:6;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:20:\\\"General,product_name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:12:\\\"Product Name\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:7;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:19:\\\"General,description\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Description\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:8;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"Variant,sku\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:3:\\\"SKU\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:9;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:12:\\\"Variant,name\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:4:\\\"Name\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:10;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:12:\\\"Variant,file\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:5:\\\"Image\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:11;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"Variant,price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:5:\\\"Price\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:12;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:24:\\\"Variant,compare_at_price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:16:\\\"Compare at Price\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:13;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:18:\\\"Variant,cost_price\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:10:\\\"Cost Price\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:14;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:15:\\\"Variant,barcode\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:13:\\\"UPC \\/ Barcode\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:15;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:14:\\\"Variant,weight\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:6:\\\"Weight\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:16;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:19:\\\"Variant,weight_unit\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:11:\\\"Weight Unit\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:17;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:22:\\\"Variant,track_quantity\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:14:\\\"Track Quantity\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:18;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:24:\\\"Variant,continue_selling\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:16:\\\"Continue Selling\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:19;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:28:\\\"Inventory -> Tanzayb Store,1\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:23:\\\"Tanzayb Store Warehouse\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:20;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:11:\\\"SEO,seo_url\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:8:\\\"URL Slug\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:21;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:13:\\\"SEO,seo_title\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:9:\\\"SEO Title\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:22;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:19:\\\"SEO,seo_description\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:15:\\\"SEO Description\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}i:23;a:5:{s:4:\\\"from\\\";a:1:{i:0;s:15:\\\"SEO,seo_keyword\\\";}s:12:\\\"with_formula\\\";s:6:\\\"assign\\\";s:2:\\\"to\\\";a:1:{i:0;s:4:\\\"Tags\\\";}s:4:\\\"with\\\";N;s:7:\\\"replace\\\";N;}}}s:7:\\\"version\\\";s:1:\\\"1\\\";s:7:\\\"catalog\\\";s:1:\\\"1\\\";s:15:\\\"ignore_unmapped\\\";s:3:\\\"off\\\";s:9:\\\"temp_name\\\";s:4:\\\"5555\\\";s:7:\\\"temp_id\\\";i:2;s:6:\\\"status\\\";s:1:\\\"1\\\";s:11:\\\"export_type\\\";s:6:\\\"custom\\\";s:13:\\\"import_action\\\";N;s:11:\\\"temp_status\\\";s:2:\\\"on\\\";}s:12:\\\"version_name\\\";s:5:\\\"EN-US\\\";s:8:\\\"filename\\\";s:55:\\\"temp_files\\/custom_en_us_products_2025_06_26_110912.xlsx\\\";s:4:\\\"user\\\";O:8:\\\"App\\\\User\\\":32:{s:13:\\\"\\u0000*\\u0000connection\\\";s:5:\\\"mysql\\\";s:8:\\\"\\u0000*\\u0000table\\\";s:5:\\\"users\\\";s:13:\\\"\\u0000*\\u0000primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"\\u0000*\\u0000keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"\\u0000*\\u0000with\\\";a:0:{}s:12:\\\"\\u0000*\\u0000withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:0;s:10:\\\"\\u0000*\\u0000perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"\\u0000*\\u0000escapeWhenCastingToString\\\";b:0;s:13:\\\"\\u0000*\\u0000attributes\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-26 07:35:07\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-26 07:35:07\\\";}s:11:\\\"\\u0000*\\u0000original\\\";a:16:{s:2:\\\"id\\\";i:1;s:9:\\\"google_id\\\";s:21:\\\"111325706919938389600\\\";s:15:\\\"shopify_shop_id\\\";N;s:13:\\\"freshworks_id\\\";N;s:5:\\\"fname\\\";s:12:\\\"Bilal Arshad\\\";s:5:\\\"lname\\\";N;s:5:\\\"email\\\";s:23:\\\"<EMAIL>\\\";s:2:\\\"ip\\\";N;s:17:\\\"email_verified_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:8:\\\"password\\\";N;s:5:\\\"phone\\\";N;s:10:\\\"last_login\\\";s:19:\\\"2025-06-26 07:35:07\\\";s:14:\\\"remember_token\\\";s:60:\\\"8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE\\\";s:12:\\\"block_status\\\";i:0;s:10:\\\"created_at\\\";s:19:\\\"2025-06-23 08:21:56\\\";s:10:\\\"updated_at\\\";s:19:\\\"2025-06-26 07:35:07\\\";}s:10:\\\"\\u0000*\\u0000changes\\\";a:0:{}s:8:\\\"\\u0000*\\u0000casts\\\";a:1:{s:17:\\\"email_verified_at\\\";s:8:\\\"datetime\\\";}s:17:\\\"\\u0000*\\u0000classCastCache\\\";a:0:{}s:21:\\\"\\u0000*\\u0000attributeCastCache\\\";a:0:{}s:13:\\\"\\u0000*\\u0000dateFormat\\\";N;s:10:\\\"\\u0000*\\u0000appends\\\";a:0:{}s:19:\\\"\\u0000*\\u0000dispatchesEvents\\\";a:0:{}s:14:\\\"\\u0000*\\u0000observables\\\";a:0:{}s:12:\\\"\\u0000*\\u0000relations\\\";a:0:{}s:10:\\\"\\u0000*\\u0000touches\\\";a:0:{}s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"\\u0000*\\u0000hidden\\\";a:2:{i:0;s:8:\\\"password\\\";i:1;s:14:\\\"remember_token\\\";}s:10:\\\"\\u0000*\\u0000visible\\\";a:0:{}s:11:\\\"\\u0000*\\u0000fillable\\\";a:9:{i:0;s:5:\\\"fname\\\";i:1;s:5:\\\"lname\\\";i:2;s:5:\\\"email\\\";i:3;s:8:\\\"password\\\";i:4;s:17:\\\"verification_code\\\";i:5;s:5:\\\"phone\\\";i:6;s:17:\\\"email_verified_at\\\";i:7;s:15:\\\"shopify_shop_id\\\";i:8;s:10:\\\"last_login\\\";}s:10:\\\"\\u0000*\\u0000guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}s:20:\\\"\\u0000*\\u0000rememberTokenName\\\";s:14:\\\"remember_token\\\";s:14:\\\"\\u0000*\\u0000accessToken\\\";N;}s:15:\\\"organization_id\\\";s:1:\\\"1\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.885392, "duration": 0.03639, "duration_str": "36.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.109, "width_percent": 57.891}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}, "Apimio\\MappingConnectorPackage\\models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => primeOrPaidUser,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2112219440 data-indent-pad=\"  \"><span class=sf-dump-note>primeOrPaidUser </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">primeOrPaidUser</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112219440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862988, "xdebug_link": null}]}, "session": {"_token": "8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/export/step1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "data": "array:5 [\n  \"file_path\" => null\n  \"input_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:5 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:6 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n          \"status\" => \"Product Status\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Export\"\n    \"nodes\" => []\n  ]\n  \"data_required\" => array:9 [\n    \"organization_id\" => \"1\"\n    \"output_type\" => \"export\"\n    \"template_method_type\" => \"export\"\n    \"apimio_plans_access\" => array:2 [\n      \"prime_user\" => true\n      \"template_save\" => true\n    ]\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.export\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"filter_query\" => array:2 [\n      \"filter_export_productIds\" => \"all\"\n      \"filter_total_product_array\" => null\n    ]\n  ]\n  \"template_attributes\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products/export", "action_name": "products.export", "controller_action": "App\\Http\\Controllers\\Product\\ExportController@export_csv", "uri": "POST products/export", "controller": "App\\Http\\Controllers\\Product\\ExportController@export_csv<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FExportController.php&line=210\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FExportController.php&line=210\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ExportController.php:210-317</a>", "middleware": "web, check_billing, prime.user", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f3f28bf-92b4-49f1-b03b-4bb5224a5ccd\" target=\"_blank\">View in Telescope</a>", "duration": "2.42s", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1524682500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1524682500\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-260623164 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  \"<span class=sf-dump-key>file_path</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n  \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:24</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Product Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>replace</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>catalog</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>ignore_unmapped</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n  \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5555</span>\"\n  \"<span class=sf-dump-key>temp_id</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>export_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">custom</span>\"\n  \"<span class=sf-dump-key>import_action</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>temp_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260623164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-413020941 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2648</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkI4NVQ5NCtiTGtsVmpsSWtjbjdyTlE9PSIsInZhbHVlIjoiSVdIaDkvUVVjVzBmYU5ScUJsVHcxYTFzb2JxYjFlbi90T1E2K0QrSFZtSXQ4amVQZ1pCYTlLRENHeE5BV3NFZ2w4eXJvZWt1SWJWWmREeXE3NWhvSHdYSkFKZ2s1aStTOUxJeHFVTWQ1MTBTN1B6ZDMzcnBJRUlBMGtLV1JDTU8iLCJtYWMiOiI0MjdmY2RlOGFhNzgxYTk0NGNmODJhMDAyYmJlNjNjOGFjMGY4YTMxMzhiZWNlNzA0YjEwNzNiNjI2MTFhZTkyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">http://localhost:8000/products/export/template/mapping</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJORzV3YXNOSkthSUNCa0JUYWVZclE9PSIsInZhbHVlIjoiZzB1K1FaMUVBTHRvaGQreFNOd1hRTytSSnpTY1lTU1BxRVRpNGpEL00xbkM2ZUNOWFdhbFc5Z1NkSWVtMjdxV1Vtbm5XekxTTzl0RXlwRUhsL1VxWmJybkppQlUxMmRIY0xYaEVoR0t4VUN0V3BNM1NUZGRYMHdsQ1lKdDRRWXo4akE3Z0c3LzhlR1JVY3E5OWFBN1hnPT0iLCJtYWMiOiJhODM4ZGFjODBjZDk4YzBkMjMxYWFiMzdkYWE3MWQ5ZGVhYTEwNjU1MTNhOTFmNDZjNDllN2MxNTQ4YzM1YjJhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkI4NVQ5NCtiTGtsVmpsSWtjbjdyTlE9PSIsInZhbHVlIjoiSVdIaDkvUVVjVzBmYU5ScUJsVHcxYTFzb2JxYjFlbi90T1E2K0QrSFZtSXQ4amVQZ1pCYTlLRENHeE5BV3NFZ2w4eXJvZWt1SWJWWmREeXE3NWhvSHdYSkFKZ2s1aStTOUxJeHFVTWQ1MTBTN1B6ZDMzcnBJRUlBMGtLV1JDTU8iLCJtYWMiOiI0MjdmY2RlOGFhNzgxYTk0NGNmODJhMDAyYmJlNjNjOGFjMGY4YTMxMzhiZWNlNzA0YjEwNzNiNjI2MTFhZTkyIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IitsOEMvMjNrS1J5am01R0FSZ1FDK3c9PSIsInZhbHVlIjoiZ2wxS243TzY4Q0FZN1drQVo3MlFYYTdEMVYzN0tsdUpJUlA5b0xPajB3YytPYzhmTjFoL3c1T2JVWnpXbXg3cUh0aFVSN2tvcWxwSVU5dTE3RWtGZi9PYUtxRElVT3dza2Vxc2hXZTJsM041ZS9NMlBxWGIzVGlmT3FpdVRQZGgiLCJtYWMiOiIyNmIxOTg0ZDlmMTFhNzg1NDZiYWYzY2U3ZWI3Y2VmM2VlZTNiYTQxYjA1NDA3ZmNmNWYwYzMzNGZkYjk0OTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413020941\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726309950 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mkkZyW6FwGI2plywjexah8GhcGowWkkfsl8V58MO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726309950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1827585358 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 11:09:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827585358\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-669548707 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/products/export/step1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file_path</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Status</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Export</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => []\n    </samp>]\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n      \"<span class=sf-dump-key>apimio_plans_access</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>prime_user</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>template_save</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.export</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>filter_query</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>filter_export_productIds</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n        \"<span class=sf-dump-key>filter_total_product_array</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669548707\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products/export", "action_name": "products.export", "controller_action": "App\\Http\\Controllers\\Product\\ExportController@export_csv"}, "badge": null}}