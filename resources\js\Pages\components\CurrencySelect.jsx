// src/components/CurrencySelect.jsx
import React from "react";
import { Select } from "antd";
import { currencies } from "./CurrencyOptions";

const { Option } = Select;

/**
 * Controlled currency dropdown.
 * Props:
 *  - value, onChange      // passed in by AntD <Form.Item>
 *  - ...rest              // any other Select props
 */
export default function CurrencySelect({ value, onChange, ...rest }) {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="Select a currency"
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) =>
        option.children.toLowerCase().includes(input.toLowerCase())
      }
      {...rest}
    >
      {currencies.map(({ value: code, label }) => (
        <Option key={code} value={code}>
          {label}
        </Option>
      ))}
    </Select>
  );
}
