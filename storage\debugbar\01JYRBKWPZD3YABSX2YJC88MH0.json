{"__meta": {"id": "01JYRBKWPZD3YABSX2YJC88MH0", "datetime": "2025-06-27 09:31:05", "utime": **********.825094, "method": "GET", "uri": "/api/2024-12/product-quality-score", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.200687, "end": **********.825124, "duration": 0.6244370937347412, "duration_str": "624ms", "measures": [{"label": "Booting", "start": **********.200687, "relative_start": 0, "end": **********.744953, "relative_end": **********.744953, "duration": 0.****************, "duration_str": "544ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.744966, "relative_start": 0.****************, "end": **********.825127, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "80.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.7596, "relative_start": 0.***************, "end": **********.764524, "relative_end": **********.764524, "duration": 0.*****************, "duration_str": "4.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.817853, "relative_start": 0.****************, "end": **********.818614, "relative_end": **********.818614, "duration": 0.0007610321044921875, "duration_str": "761μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.820975, "relative_start": 0.****************, "end": **********.821102, "relative_end": **********.821102, "duration": 0.00012683868408203125, "duration_str": "127μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/product-quality-score", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:128-155</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.004959999999999999, "accumulated_duration_str": "4.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7868161, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 81.048}, {"sql": "select\nSUM(CASE WHEN mean_score >= 90 THEN 1 ELSE 0 END) AS good,\nSUM(CASE WHEN mean_score >= 50 AND mean_score < 90 THEN 1 ELSE 0 END) AS fair,\nSUM(CASE WHEN mean_score < 50 THEN 1 ELSE 0 END) AS bad\nfrom (select products.id, AVG(product_version.score) as mean_score from `products` inner join `product_version` on `products`.`id` = `product_version`.`product_id` where `organization_id` = 1 group by `products`.`id`) as avg_scores limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 145}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.809999, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:145", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=145", "ajax": false, "filename": "DashboardController.php", "line": "145"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.048, "width_percent": 18.952}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/product-quality-score\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore", "uri": "GET api/2024-12/product-quality-score", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:128-155</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4108a2-e6f6-405e-9840-16dc7170d55a\" target=\"_blank\">View in Telescope</a>", "duration": "627ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1744043656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744043656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2015621971 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2015621971\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1498220302 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVSUXZMdXhyWUFvUTh3ZFJMRTJ2SkE9PSIsInZhbHVlIjoiQiswY3YzUEIyUi96RXJoWkRNVHJYYWZRMmJtS3NscVd0ZHo2NlNYY21LbWg3UVRaRnFJNzNnc0M5V3FLMVRoVTVOU1hLNlpBNmg1akFGY0hDT1FDWFRpczdsallzeFczWnV2QWJXZHIwZTdMN0xqSnVYQURsUFQ3SzU3ZU1OTnciLCJtYWMiOiIxNjA3NmNiMDI4MjcwNmVkYTcwMWUzMjk2NTQ0Y2ZiZmZlOGI4MjNlYTRhZTY2YzM4YjkxMTVjYWJkMTU2MGNiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVzOU9OS2dHeWtJNXFnajdrWTdJNEE9PSIsInZhbHVlIjoiUGs5TEhWbFVSdFJrR1VDVGJ0UVlLRnMrWjdjZm9mS1AwZXNOL0Ixakxtazc2aWUrZzVtRXhhN2xERmRCT3NodFRtd2dvODZRdHRZZmpwNDhWcmdBakl3R0U5SGI0TmFkbGNZWTVTVkhrclZCZGk2eWo0emNsSFJ3T1Q0THpXZXYyeExTRitjclJSK1NVTmpwUkFOSG9BPT0iLCJtYWMiOiJkMjNhODZkNzc0ZDNiMDQ3NjhlNWU0MDcwNDVjMWJmM2RhNTkxODk1MzNiN2M2OTkxMDQyNmVlNmU2OGI5M2ZhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVSUXZMdXhyWUFvUTh3ZFJMRTJ2SkE9PSIsInZhbHVlIjoiQiswY3YzUEIyUi96RXJoWkRNVHJYYWZRMmJtS3NscVd0ZHo2NlNYY21LbWg3UVRaRnFJNzNnc0M5V3FLMVRoVTVOU1hLNlpBNmg1akFGY0hDT1FDWFRpczdsallzeFczWnV2QWJXZHIwZTdMN0xqSnVYQURsUFQ3SzU3ZU1OTnciLCJtYWMiOiIxNjA3NmNiMDI4MjcwNmVkYTcwMWUzMjk2NTQ0Y2ZiZmZlOGI4MjNlYTRhZTY2YzM4YjkxMTVjYWJkMTU2MGNiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImJjY2QxaXdkRXVpWjRnbHlnY01CR3c9PSIsInZhbHVlIjoiK1oyaEpEWW1QdjAybjE2QVlwYVVGNEUzcFRReU5kN0NOZzNoNGs3RGpmNytuQjArOVJnMWpJUWlQcXR0dDVHNXc5ZjY2MFp3ZGpaajV1aEFqaUg3ZE9taE1mTWwyQXd0L1FGWFUzWWdoOUlPdVh2cTRrcjF5NXRwL1M0cVVCRUYiLCJtYWMiOiJjYzFkMmRkMzVkY2U2YTIwZjE1YjEzMTJiMDcxZjNjOWNjNDRjOTM2NjE5MDA4Y2U5ODAzMGRjMWZkOTZmMjZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498220302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1370585343 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|IbeS3I2W02eAkiae8I30deZtxSJuBVzomEN4VW4GfzaWyD7XMplnXtrAPk3Q|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o4qeOX4JeNGwBuCfe3fqu5iqZVUYsFaZmtFfdug8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370585343\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1607566179 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:31:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjRkeHdpT2hLM3pXWCtqelhXY1h0c2c9PSIsInZhbHVlIjoiVDZ3OGw2UlFuLzRiVWZ2OGs3SVdmQVc1a21vbkJDWjZCZ2xFMEZyWW10Qm9DWmp0ckk3dVI5ZVdlQVhNNGVxbFA4TXQ3eEpKTHRDei9ZZ1ozTmJBUkNiR2lVOGFsbDBRUU1kOWxWNmRyS1JzTFFERXl4bUlxTXBYRXh6cUhYRjkiLCJtYWMiOiJhZDQyOWMxM2Q3M2FhMTI4ZDI1MDQxMzhkNDZiYTFiNTlmNWMwNzNhMTMyNzg3Y2EwMjFjZjdmMjlhMGM1ZWEwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:31:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Ikc0UjR1a2R5ZUl6OTdWSFMvYWxRMVE9PSIsInZhbHVlIjoiREZNRnNqMmJMYWE2S0N3eUpCcHRLTVZRN0NZa1FSSjdBcGVEVlpvdWpjcVJxQTZFSTFxbWJMUzlZOWhOU0ljMERtV3Z6MVNSc0ZqSWJHRXNUWi9IR0Z3TTcwcnhHNlFQZTV2WGE2a05YVGNraVhNNWFZSGFvK3JPL0x2cVRibEIiLCJtYWMiOiIzZGZlYmRlMGJiYTdjNzNkMzg1YWU5OGUwY2Y4MzA1NzU0NTQ1ZDBkYTdhZmVlZTFlNjllNzQ2NmQ1MjA2NTEzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 11:31:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRkeHdpT2hLM3pXWCtqelhXY1h0c2c9PSIsInZhbHVlIjoiVDZ3OGw2UlFuLzRiVWZ2OGs3SVdmQVc1a21vbkJDWjZCZ2xFMEZyWW10Qm9DWmp0ckk3dVI5ZVdlQVhNNGVxbFA4TXQ3eEpKTHRDei9ZZ1ozTmJBUkNiR2lVOGFsbDBRUU1kOWxWNmRyS1JzTFFERXl4bUlxTXBYRXh6cUhYRjkiLCJtYWMiOiJhZDQyOWMxM2Q3M2FhMTI4ZDI1MDQxMzhkNDZiYTFiNTlmNWMwNzNhMTMyNzg3Y2EwMjFjZjdmMjlhMGM1ZWEwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:31:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Ikc0UjR1a2R5ZUl6OTdWSFMvYWxRMVE9PSIsInZhbHVlIjoiREZNRnNqMmJMYWE2S0N3eUpCcHRLTVZRN0NZa1FSSjdBcGVEVlpvdWpjcVJxQTZFSTFxbWJMUzlZOWhOU0ljMERtV3Z6MVNSc0ZqSWJHRXNUWi9IR0Z3TTcwcnhHNlFQZTV2WGE2a05YVGNraVhNNWFZSGFvK3JPL0x2cVRibEIiLCJtYWMiOiIzZGZlYmRlMGJiYTdjNzNkMzg1YWU5OGUwY2Y4MzA1NzU0NTQ1ZDBkYTdhZmVlZTFlNjllNzQ2NmQ1MjA2NTEzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 11:31:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607566179\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-46349152 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/api/2024-12/product-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46349152\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore"}, "badge": null}}