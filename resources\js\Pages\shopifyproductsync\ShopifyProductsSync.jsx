// resources/js/v2/pages/onboarding/ShopifyProductsSync.jsx

import React, { useState, useEffect } from "react";
import { Layout, Button, Form, Input, Modal, Image, message } from "antd";
import RightArrow from "../../../../public/v2/icons/right-arrow.svg";
import ExcelIcon from "../../../../public/v2/icons/excel-icon.svg";
import ShopifyIcon from "../../../../public/v2/icons/shopify-icon.svg";
import BoxIcon from "../../../../public/v2/icons/box-icon.svg";
import ReactDOM from "react-dom/client";
import { router } from "@inertiajs/react";
import ShopifyStepTwo from "../../components/ShopifyStepTwo";

const { Content } = Layout;

const ShopifyProductsSync = () => {
    // State variables
    const [currentStep, setCurrentStep] = useState(1); // 1: Select Import Method, 2: Sync Products
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [shopifyurl, setShopifyurl] = useState("");
    const [urlError, setUrlError] = useState("");

    // Options for selecting import method
    const options = [
        {
            id: 1,
            name: "Import Your Existing CSV",
            img: ExcelIcon,
            fun: () => {
                router.visit("/products/import/step1");
            },
        },
        {
            id: 2,
            name: "Connect Your Shopify Store to Import Products",
            img: ShopifyIcon,
            fun: () => {
                setIsModalVisible(true);
            },
        },
        {
            id: 3,
            name: "Add Product Manually",
            img: BoxIcon,
            fun: () => {
                router.visit("/products");
            },
        },
    ];

    const currentPath = window.location.pathname;
        // Effect to clear localStorage when user reaches the last step or navigates to /syncproducts
        useEffect(() => {
            console.log("Current step:",currentPath );  // Log current step
    
            if (typeof window !== 'undefined') {
                
    
                // Check if the path contains "/syncproducts" and if we're at the last step
                if (currentPath.includes("/syncproducts") ) {
                    console.log("Completed all steps. Clearing localStorage...");
    
                    // Remove data from localStorage when step 7 (or last step) is completed
                    localStorage.removeItem('onboarding_current_step');
                    localStorage.removeItem('onboarding_form_data');
                    localStorage.removeItem('onboarding_shopify_url');
                } 
            }
        }, [ currentPath]);

    /**
     * Validates a Shopify store URL
     * @param {string} url - The URL to validate
     * @returns {boolean} - Whether the URL is valid
     */
    const validateShopifyUrl = (url) => {
        if (!url || !url.trim()) {
            return false;
        }

        try {
            // Handle URLs with or without protocol
            let cleanUrl = url.trim();

            // Remove protocol if present (http://, https://)
            cleanUrl = cleanUrl.replace(/^https?:\/\//, "");

            // Remove www. if present
            cleanUrl = cleanUrl.replace(/^www\./, "");

            // Remove trailing slash if present
            cleanUrl = cleanUrl.replace(/\/$/, "");

            // Check if the URL matches Shopify store pattern
            const shopifyPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/;
            return shopifyPattern.test(cleanUrl);
        } catch (error) {
            return false;
        }
    };

    /**
     * Validates the current URL input and updates error state
     * Returns true if valid, false if invalid
     */
    const validateInput = () => {
        if (!shopifyurl.trim()) {
            setUrlError("Please enter your Shopify store URL");
            return false;
        }

        if (!validateShopifyUrl(shopifyurl)) {
            setUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
            return false;
        }

        setUrlError("");
        return true;
    };

    /**
     * Handles the "Connect" action after entering the Shopify URL.
     * Validates the URL and proceeds to the next step.
     */
    const handleConnect = () => {
        if (validateInput()) {
            setIsModalVisible(false);
            setCurrentStep(2);
        }
    };



    // Render the initial step: Selecting import method
    const renderStepOne = () => (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] min-h-screen">
            <h2 className="text-[40px] text-center font-bold pb-[10px]">Start Onboarding Your Products</h2>
            <p className="text-gray-500 text-center mb-6">Yay! You are just one more step away.</p>

            {options.map((opt) => (
                <div
                    key={opt.id}
                    onClick={opt.fun}
                    className="max-w-4xl cursor-pointer mx-auto mb-[20px] p-[18px] bg-[white] rounded-[12px] border border-[#D9D9D9]"
                >
                    <div className="flex items-center">
                        <div className="w-1/2 flex items-center gap-[17px]">
                            <Image src={opt.img} alt={`${opt.name} Icon`} />
                            <p className="font-[700] text-[16px]">{opt.name}</p>
                        </div>
                        <div className="w-1/2 flex justify-end">
                            <Image src={RightArrow} alt="right arrow" />
                        </div>
                    </div>
                </div>
            ))}

            <div className="flex justify-center mt-6 gap-[30px]">
                <Button
                    className="bg-[#740898] text-[14px] rounded-[4px] mt-[40px] font-[400] h-[32px] px-[16px] py-[4px] border border-[#740898] text-[#FFFFFF]"
                    onClick={() => router.visit("/dashboard")}
                >
                    Continue to Dashboard
                </Button>
            </div>

            {/* Modal for entering Shopify URL */}
            <Modal
                centered
                title={<span className="text-[24px] font-[700]">Enter Your Store URL</span>}
                visible={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                footer={null}
                closable={false}
                width={640}
            >
                <div className="space-y-4 w-[600px]">
                    <Form layout="vertical">
                        <Form.Item label="Shop URL" validateStatus={urlError ? "error" : ""} help={urlError}>
                            <Input
                                value={shopifyurl}
                                onChange={(e) => {
                                    const newValue = e.target.value;
                                    setShopifyurl(newValue);

                                    // Don't validate empty inputs instantly to avoid showing errors as user starts typing
                                    if (!newValue.trim()) {
                                        setUrlError("Please enter your Shopify store URL");
                                    } else if (!validateShopifyUrl(newValue)) {
                                        setUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
                                    } else {
                                        setUrlError("");
                                    }
                                }}
                                placeholder="your-shop-url.myshopify.com"
                                className="rounded-[2px]"
                                width={640}
                            />
                            <p className="text-[#626262] text-[14px] font-[400] mt-[8px]">
                                Haven't created a store yet? Learn more on&nbsp;
                                <a
                                    href="https://www.shopify.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-[#740898] underline"
                                >
                                    shopify.com
                                </a>
                            </p>
                        </Form.Item>
                        <div className="flex justify-end gap-4">
                            <Button onClick={() => setIsModalVisible(false)}>Cancel</Button>
                            <Button
                                type="primary"
                                onClick={handleConnect}
                                disabled={!shopifyurl.trim() || urlError !== ""}
                                className="bg-[#740898] text-[#FFFFFF] border-[#740898]"
                            >
                                Connect
                            </Button>
                        </div>
                    </Form>
                </div>
            </Modal>
        </div>
    );

    // Render the second step: Sync Products using the reusable component
    const renderStepTwo = () => <ShopifyStepTwo shopifyUrl={shopifyurl} />;

    return (
        <Layout>
            <Content className="flex flex-col md:flex-row">
                {currentStep === 1 && renderStepOne()}
                {currentStep === 2 && renderStepTwo()}
            </Content>
        </Layout>
    );
};

export default ShopifyProductsSync;

const rootElement = document.getElementById("v2-syncproducts");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<ShopifyProductsSync />);
}
