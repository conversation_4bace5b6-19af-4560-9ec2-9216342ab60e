
<?php $__env->startSection('titles','Gallery'); ?>
<?php $__env->startSection('content'); ?>
    <?php
        $variable = 'value2';
    ?>
    <?php $__env->startPush("header_scripts"); ?>
        
        <link rel="stylesheet" href="<?php echo e(asset('css/gallery/css/checkbox.css')); ?>" type="text/css"/>
        <link rel="stylesheet" href="<?php echo e(asset('gallery_assets/scss/screen.css')); ?>" type="text/css"/>
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.min.css">

        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.js"></script>
    <?php $__env->stopPush(); ?>
    <style>
        .download-btn {
            display: none !important
        }

        #folder-main2 {
            display: none;
        }

        #hide-folders {
            display: none;
        }
        .media-badges-score{
            padding: 8px 20px !important;
        }

    </style>
    <div class="d-flex justify-content-between asset_header" style="margin-bottom: -50px;">
        <div class="mb-2">

            <h2 class="m-0">Media</h2>
            <p class=" asset_description"> Digital asset management (DAM) helps you get more value from creative digital
                assets like images by making them easy to organize, access and distribute.</p>
        </div>
        <div class=" btn_section">
            <?php if (isset($component)) { $__componentOriginal7e8f2c570d74753dc17cf226cba23a3c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7e8f2c570d74753dc17cf226cba23a3c = $attributes; } ?>
<?php $component = Apimio\Gallery\components\AssetButton::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-asset-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\AssetButton::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7e8f2c570d74753dc17cf226cba23a3c)): ?>
<?php $attributes = $__attributesOriginal7e8f2c570d74753dc17cf226cba23a3c; ?>
<?php unset($__attributesOriginal7e8f2c570d74753dc17cf226cba23a3c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7e8f2c570d74753dc17cf226cba23a3c)): ?>
<?php $component = $__componentOriginal7e8f2c570d74753dc17cf226cba23a3c; ?>
<?php unset($__componentOriginal7e8f2c570d74753dc17cf226cba23a3c); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\FolderCreate::resolve(['id' => 'create_folder','header' => 'Create Folder','title' => 'Folder Name','btnText' => 'Save','formUrl' => ''.e(route('gallery.store')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-folder-create'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\FolderCreate::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $attributes = $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $component = $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>

        </div>
    </div>

    
    
    <h2 class="mt-4 mb-1" style="margin-top:48px !important;">Folders</h2>
    <div class="collection " id="folder-main">
        <?php $__currentLoopData = $data['folders']->slice(0,12); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if (isset($component)) { $__componentOriginal2246dcd1626629c35995a73d7cb68333 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2246dcd1626629c35995a73d7cb68333 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\Folder::resolve(['name' => ''.e($folder->name).'','id' => ''.e($folder->id).'','link' => ''.e($folder->link).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-folder'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\Folder::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2246dcd1626629c35995a73d7cb68333)): ?>
<?php $attributes = $__attributesOriginal2246dcd1626629c35995a73d7cb68333; ?>
<?php unset($__attributesOriginal2246dcd1626629c35995a73d7cb68333); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2246dcd1626629c35995a73d7cb68333)): ?>
<?php $component = $__componentOriginal2246dcd1626629c35995a73d7cb68333; ?>
<?php unset($__componentOriginal2246dcd1626629c35995a73d7cb68333); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>
    <div class="collection " id="folder-main2">
        <?php $__currentLoopData = $data['folders']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if (isset($component)) { $__componentOriginal2246dcd1626629c35995a73d7cb68333 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2246dcd1626629c35995a73d7cb68333 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\Folder::resolve(['name' => ''.e($folder->name).'','id' => ''.e($folder->id).'','link' => ''.e($folder->link).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-folder'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\Folder::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2246dcd1626629c35995a73d7cb68333)): ?>
<?php $attributes = $__attributesOriginal2246dcd1626629c35995a73d7cb68333; ?>
<?php unset($__attributesOriginal2246dcd1626629c35995a73d7cb68333); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2246dcd1626629c35995a73d7cb68333)): ?>
<?php $component = $__componentOriginal2246dcd1626629c35995a73d7cb68333; ?>
<?php unset($__componentOriginal2246dcd1626629c35995a73d7cb68333); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>
    <?php if(count($data['folders'])>12): ?>
        <button type="button"
                class="mt-1 all-folders"
                id="show-folders"
                style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
            Show all (<?php echo e(count($data['folders'])); ?>)
        </button>
    <?php endif; ?>
    <button type="button"
            class="mt-1"
            id="hide-folders"
            style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
        Show less
    </button>

    
    <div>
        <h2 class="mt-3">Storage</h2>
        <div class="progress mb-1" style="width: 100%; height: 10px">
            <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                 style="width:<?php echo e(($data['plan']->sum/(isset($data['plan']->subplan->storage) ? $data['plan']->subplan->storage : 2))*100); ?>%"></div>

        </div>
        <p class="mb-4 storage-text ml-1"><?php echo e($data['plan']->sum); ?> GB
            of <?php echo e((isset($data['plan']->subplan->storage) ? $data['plan']->subplan->storage : 2)); ?> GB.</p>


        

        <div>
            <?php if (isset($component)) { $__componentOriginal3f9dda6c81f32d9b34bcf0a258165da1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3f9dda6c81f32d9b34bcf0a258165da1 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\ScoringGraphByProduct::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-scoring-graph-by-product'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\ScoringGraphByProduct::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3f9dda6c81f32d9b34bcf0a258165da1)): ?>
<?php $attributes = $__attributesOriginal3f9dda6c81f32d9b34bcf0a258165da1; ?>
<?php unset($__attributesOriginal3f9dda6c81f32d9b34bcf0a258165da1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3f9dda6c81f32d9b34bcf0a258165da1)): ?>
<?php $component = $__componentOriginal3f9dda6c81f32d9b34bcf0a258165da1; ?>
<?php unset($__componentOriginal3f9dda6c81f32d9b34bcf0a258165da1); ?>
<?php endif; ?>
        </div>
        <div class="d-flex justify-content-between mt-3">
            <?php if( $data['files']->total()>0): ?>

                <div>


                    <h2 class="mb-1">Images</h2>
                    <p class="page-subheading mb-4 total_img">Total Images: <?php echo e($data['files']->total()); ?></p>
                </div>

                <ul class="nav nav-tabs" role="tablist" style="float: right; align-self: center">
                    <li class="nav-item nav-pills">
                        <a class="nav-link active"
                           data-bs-toggle="tab"
                           href="#tabs-1">
                            <img src="https://svgshare.com/i/po3.svg" alt="L" style="height: 14px">
                        </a>
                    </li>
                    <li class="nav-item nav-pills">
                        <a class="nav-link"
                           data-bs-toggle="tab"
                           href="#tabs-2">
                            <img src="https://svgur.com/i/po4.svg" alt="B" style="height: 14px">
                        </a>
                    </li>
                </ul>
        </div>
        <?php endif; ?>

        <?php if( $data['files']->total()==0): ?>
            <p class="Roboto mx-auto text-center mt-5 " style="font-weight: 400; margin-top: 150px !important;">
                No files found.
            </p>
        <?php endif; ?>
        <?php if( $data['files']->total()>0): ?>


            <div class="tab-content">
                <div class="tab-pane active" id="tabs-1">
                    <div>
                        <?php if (isset($component)) { $__componentOriginald63cdc66c8c5346d3ad3f706ad18dc88 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald63cdc66c8c5346d3ad3f706ad18dc88 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\AssetTable::resolve(['files' => $data['files']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-asset-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\AssetTable::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald63cdc66c8c5346d3ad3f706ad18dc88)): ?>
<?php $attributes = $__attributesOriginald63cdc66c8c5346d3ad3f706ad18dc88; ?>
<?php unset($__attributesOriginald63cdc66c8c5346d3ad3f706ad18dc88); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald63cdc66c8c5346d3ad3f706ad18dc88)): ?>
<?php $component = $__componentOriginald63cdc66c8c5346d3ad3f706ad18dc88; ?>
<?php unset($__componentOriginald63cdc66c8c5346d3ad3f706ad18dc88); ?>
<?php endif; ?>
                    </div>
                </div>

                <div class="tab-pane" id="tabs-2">
                    <?php if (isset($component)) { $__componentOriginal84ad088d416f8097572e547e876af92f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal84ad088d416f8097572e547e876af92f = $attributes; } ?>
<?php $component = Apimio\Gallery\components\AssetCard::resolve(['files' => $data['files']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-asset-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\AssetCard::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal84ad088d416f8097572e547e876af92f)): ?>
<?php $attributes = $__attributesOriginal84ad088d416f8097572e547e876af92f; ?>
<?php unset($__attributesOriginal84ad088d416f8097572e547e876af92f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal84ad088d416f8097572e547e876af92f)): ?>
<?php $component = $__componentOriginal84ad088d416f8097572e547e876af92f; ?>
<?php unset($__componentOriginal84ad088d416f8097572e547e876af92f); ?>
<?php endif; ?>
                </div>
            </div>
        <?php endif; ?>



        <?php echo $data['files']->links(); ?>


        <?php if (isset($component)) { $__componentOriginale7ff6cd309ca20d7320da2b6d2a67865 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale7ff6cd309ca20d7320da2b6d2a67865 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\ImageUploader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-image-uploader'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\ImageUploader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale7ff6cd309ca20d7320da2b6d2a67865)): ?>
<?php $attributes = $__attributesOriginale7ff6cd309ca20d7320da2b6d2a67865; ?>
<?php unset($__attributesOriginale7ff6cd309ca20d7320da2b6d2a67865); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale7ff6cd309ca20d7320da2b6d2a67865)): ?>
<?php $component = $__componentOriginale7ff6cd309ca20d7320da2b6d2a67865; ?>
<?php unset($__componentOriginale7ff6cd309ca20d7320da2b6d2a67865); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginalb2dfe8796758629fb9e61e58b8e78944 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb2dfe8796758629fb9e61e58b8e78944 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\RenameFolder::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-rename-folder'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\RenameFolder::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb2dfe8796758629fb9e61e58b8e78944)): ?>
<?php $attributes = $__attributesOriginalb2dfe8796758629fb9e61e58b8e78944; ?>
<?php unset($__attributesOriginalb2dfe8796758629fb9e61e58b8e78944); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb2dfe8796758629fb9e61e58b8e78944)): ?>
<?php $component = $__componentOriginalb2dfe8796758629fb9e61e58b8e78944; ?>
<?php unset($__componentOriginalb2dfe8796758629fb9e61e58b8e78944); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal484d4f1cbac73a5940cc4b05999e9277 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal484d4f1cbac73a5940cc4b05999e9277 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\UnlinkProduct::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-unlink-product'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\UnlinkProduct::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal484d4f1cbac73a5940cc4b05999e9277)): ?>
<?php $attributes = $__attributesOriginal484d4f1cbac73a5940cc4b05999e9277; ?>
<?php unset($__attributesOriginal484d4f1cbac73a5940cc4b05999e9277); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal484d4f1cbac73a5940cc4b05999e9277)): ?>
<?php $component = $__componentOriginal484d4f1cbac73a5940cc4b05999e9277; ?>
<?php unset($__componentOriginal484d4f1cbac73a5940cc4b05999e9277); ?>
<?php endif; ?>
        <?php $__env->stopSection(); ?>
        <?php $__env->startPush('footer_scripts'); ?>
            <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

            <?php $__errorArgs = ['folder_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>

            <script>
                $("#create_folder").modal('show')
            </script>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>


            <script>

                $(document).ready(function () {
                    $('#modal_aside').modal('show');
                });
                //Show all folders function
                $(".all-folders").on('click', function () {
                    $("#folder-main").css("display", "none")
                    $("#show-folders").css("display", "none")
                    $("#folder-main2").css("display", "grid")
                    $("#hide-folders").css("display", "block")
                })
                $("#hide-folders").click(function () {
                    $("#folder-main").css("display", "grid")
                    $("#show-folders").css("display", "block")
                    $("#folder-main2").css("display", "none")
                    $("#hide-folders").css("display", "none")
                });

                let input = document.querySelector(".share_link")
                let folder_count = <?php echo json_encode(count($data['folders'])); ?>;
                let tooltip_name = document.querySelector("#folder-count")
                let tooltip = document.querySelector(".folder-card")
                const height_info = document.querySelector(".height_info")
                const size_info = document.querySelector(".size_info")
                const width_info = document.querySelector(".width_info")
                const type_info = document.querySelector(".type_info")
                const img_info = document.querySelector(".img_info");
                let card_info = document.querySelectorAll(".card_info")
                let file_data = document.querySelector(".file_id")
                let table_img_data = <?php echo json_encode($data['files']); ?>;

                //empty page condition
                if (table_img_data.data.length === 0) {
                    $(".tab-content").hide()
                    $(".total_img").hide()
                    $(".tabs").hide()
                }
                // Loop through each div element with the class tooltip_name

                const new_name = document.querySelectorAll(".tooltip_name")
                let folder_name = <?php echo json_encode($data['folders']); ?>;
                for (let i = 0; i <= new_name.length; i++) {

                    let tooltip_id = new_name[i];
                    if (folder_name[i].name.length > 19) {
                        $(tooltip_id).attr('data-bs-original-title', folder_name[i].name)
                    } else {
                        $(tooltip_id).attr('data-bs-original-title', '')
                    }
                }
                let download_link = document.querySelector(".download_img")


                // $("#folder-main2").hide()

                //Asset sidebar js


                // $(".card_info").click(function() {
                //     load_data(this, ".card_info")
                // });

                function initialize(element, isProgrammaticClose, isTriggered) {

                    // $("#modal_aside").show()
                    var file_id = $(element).data("id");
                    let file_ids = file_id
                    // $("#sidebar").html('<div class="loader"></div>');
                    $.ajax({
                        url: "<?php echo e(url('file/details/')); ?>/" + file_id,
                        type: "GET",
                        data: {
                            file_id: file_id
                        },
                        beforeSend: function () {
                            // show loader
                        },
                        complete: function (data) {
                            // hide loader
                            $(document).ready(function () {
                                $('#example').select2({
                                    templateResult: function (data, container) {
                                        if (!data.id) {
                                            return data.text;
                                        }
                                        let $element = $(data.element);
                                        let image_url = $element.data('image');
                                        let $wrapper = $('<span><img src="' + image_url + '" class="img-thumbnail" /> ' + data.text + '</span>');
                                        return $wrapper;
                                    },
                                    multiple: true,
                                    dropdownParent: $('#modal_aside'),
                                    placeholder: 'Search by SKU or name'

                                });
                                $('#example').val('').trigger('change');
                            });

                        },
                        success: function (data) {
                            // your success code here

                            // Initialize flag to false
                            if ($("#sidebar_modal").length > 0) {
                                // element with ID "elementId" exists
                                $("#sidebar_modal").remove();
                            }
                            if (isTriggered) {


                                $("body").get(0).insertAdjacentHTML("afterbegin", data);
                                $('#modal_aside').on('shown.bs.modal', function () {
                                    $('a[href="#profile"]').tab('show');
                                }).modal('show');
                            } else {
                                $("body").get(0).insertAdjacentHTML("afterbegin", data);
                                $("#modal_aside").modal("show");
                            }
                            // $('#example').select2({
                            // });
                            const modalElement = document.getElementById('modal_aside');
                            if (isProgrammaticClose) { // Check if modal is not being closed programmatically
                                modalElement.addEventListener('hidden.bs.modal', function (event) {
                                    // Check if the modal was closed due to a click outside the modal
                                    if (event.target === modalElement) {
                                        isTriggered = false; // Set isTriggered variable to false

                                    }
                                });
                            }
                            $(document).ready(function () {
                                $('[data-toggle="tooltip"]').tooltip();
                            });

                            $('.btn-copy').on('click', function (e) {
                                let copyText = document.getElementById("copy-input");
                                navigator.clipboard.writeText(copyText.value);
                                copyText.style.backgroundColor='blue';
                                copyText.style.color='white';
                                copyText.style.width='80%';
                                $('.btn-copy').css('min-width','60px');
                                $('.btn-copy').text('copied!');

                                setTimeout(()=>{
                                    copyText.style.backgroundColor='';
                                    copyText.style.color='';
                                    $('.btn-copy').html('<i class="fa fa-clipboard" style="color: black;" aria-hidden="true"></i>');
                                    copyText.style.width='87%';
                                    $('.btn-copy').css('min-width','40px');

                                },2000)
                                e.preventDefault()
                            });

                            let selectedIds = [];
                            $('#example').on('select2:select', function (e) {
                                var data = e.params.data;
                                let selectedOptionValue = data.id;
                                selectedIds.push(selectedOptionValue);
                            });

                            $('#example').on('select2:unselect', function (e) {
                                var data = e.params.data;
                                let selectedOptionValue = data.id;
                                let index = selectedIds.indexOf(selectedOptionValue);
                                selectedIds.splice(index, 1);
                            });


                            $('#submit_btn').click(function () {
                                file_id = file_ids

                                let data = null;
                                let file = null;
                                // if(selectedIds > 0) {
                                $.ajax({
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    url: '<?php echo e(route('assign.file')); ?>',
                                    method: 'POST',
                                    data: {
                                        product_id: selectedIds,
                                        file_id
                                    },
                                    success: function (response) {

                                        $("#modal_aside").modal("hide");
                                        isTriggered = true;
                                        isProgrammaticClose = false;

                                        initialize(element, isProgrammaticClose, isTriggered)
                                        //  $('#example').val(null).trigger('change');
                                        //  for (let i = 0; i < selectedIds.length; i++) {
                                        //      $('#example').find(`option[value="${selectedIds[i]}"]`).remove();
                                        //  }
                                        //
                                        //
                                        // data = JSON.parse(response)
                                        //  let cardDisplay = document.querySelector('.card-container');
                                        //  let html = '';
                                        //
                                        //  for (let i = 0; i < data.length; i++)
                                        //  {
                                        //       file = data[i];
                                        //      html += ` <div class="card link-card py-3 px-2 mt-1" style="margin-left: 3px; margin-right: 3px; box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;  ">
                                        //          <div class="d-flex justify-content-between custom-margin ">
                                        //              <h5 class="info-typography head-pad link-card-text ms-2 fs-16 fw-600">SKU</h5>
                                        //              <p class="asset-details-typo link-card-text me-4 fs-14" id="product_name"> ${file.sku}</p>
                                        //          </div>
                                        //
                                        //          <div class="d-flex justify-content-between">
                                        //              <h5 class="info-typography link-card-text head- ms-2 fs-16 fw-600">Product Name</h5>
                                        //              <p class="asset-details-typo link-card-text me-4  fs-14" id="pro_id" data_id="${file.id}"> ${file.id}</p>
                                        //          </div>
                                        //          <div>
                                        //              <button type="button" class="btn btn-outline-danger   px-3 ms-2 unlink_btn" id="btn_unlink" data-bs-toggle="modal" data-bs-target="#unlink_product" style="opacity: 1;">Unlink
                                        //                  product
                                        //              </button>
                                        //          </div>
                                        //      </div>`
                                        //      // const buttons = document.querySelectorAll('#btn_unlink');
                                        //
                                        //  }
                                        //  cardDisplay.innerHTML = html;

                                    },
                                    complete: function () {
                                        // Use variables after exiting AJAX call
                                        data = null
                                        file = null
                                        selectedIds = []
                                        file_id = null

                                        // Refresh the select2 dropdown to update the UI
                                        // $('#example').select2('refresh');
                                    }
                                });
                                // }
                            });
                            let selectedFolder
                            $("#folder_change").change(function () {
                                var selectedFolder = $(this).val();
                            });
                            const buttons = document.querySelectorAll('#btn_unlink');
                            let selectedID = '';
                            let card;
                            let IDS = [];
                            // let selected = []
                            buttons.forEach(button => {
                                button.addEventListener('click', function () {
                                    card = this.closest('.card');
                                    const pTag = card.querySelector('#pro_id');
                                    // const sku_name = card.querySelector('#product_name');
                                    // const new_name = sku_name.textContent;
                                    IDS = pTag.getAttribute('data_id');
                                    // IDS = selected;
                                });
                            });

                            $('#unlink').click(function () {
                                file_id = file_ids
                                // selectedID = IDS
                                if (IDS) {
                                    $.ajax({
                                        headers: {
                                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                        },
                                        url: "<?php echo e(url('unassign/file/')); ?>/" + IDS,
                                        type: "post",
                                        data: {
                                            product_id: IDS,
                                            file_id
                                        },
                                        success: function (response) {
                                            // location.reload()
                                            $("#modal_aside").modal("hide");

                                            isTriggered = true;
                                            isProgrammaticClose = false;
                                            initialize(element, isProgrammaticClose, isTriggered)
                                            // $('#modal_aside').on('shown.bs.modal', function() {
                                            //     $('#profile-tab').tab('show');
                                            // })
                                            $("#unlink_product").modal("hide");
                                            // card.style.display = 'none'
                                            // const productId = IDS
                                            // console.log(productId)
                                            // $('#example').append($('<option>', {
                                            //     value: IDS
                                            // }));

                                        },
                                        complete: function () {
                                            // Use variables after exiting AJAX call

                                            // $('a[href="#profile"]').tab('show');
                                            IDS = null;
                                            file_id = null;
                                            file_ids = null;
                                            // Refresh the select2 dropdown to update the UI
                                            // $('#example').select2('refresh');
                                        }
                                    });
                                }
                            });

                            let image_name = $("#image_name")

                            //TODO::we need to make folder folder optional
                            $('#save_details').click(function () {
                                let selectedFolder = $("#folder_change").val();
                                let inputValue = $("#image_name").val()
                                $.ajax({
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    url: "<?php echo e(route('update.file')); ?>",
                                    type: "post",
                                    data: {
                                        folder_id: selectedFolder,
                                        file_id: file_id,
                                        image_name: inputValue
                                    },
                                    success: function (response) {
                                        location.reload()


                                    }
                                });
                            });
                        },
                        error: function (error) {
                            // your error code here
                            $("#sidebar").html("<p>Something went wrong. Please refresh and try again.</p>");
                        }
                    });


                }

            </script>
            <script>


                let card_img = document.querySelector(".card-img-top")
                let image_modal = document.querySelector(".image-upload")
                let image_modal_close = document.querySelector(".image-move")

                function open_side() {
                    $("#modal_aside").modal('toggle');
                }

                //    Css Conditional Rendering

                //Image uploading modal
                function close_image_modal() {
                    image_modal.style.display = 'none'
                }

                function open_image_modal() {
                    image_modal.style.display = 'block'
                }

                //    Divider
                let main = document.getElementById('folder-main')
                document.getElementById('divider-bar').addEventListener('click', function () {

                    (main.style.height === '103px' || main.style.height === '')
                        ? main.style.height = 'auto'
                        : main.style.height = '103px';

                }, false);


            </script>
    <?php $__env->stopPush(); ?>


    
    
    
    
    

<?php echo $__env->make('gallery::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\gallery\src\Providers/../views/index.blade.php ENDPATH**/ ?>