import React, { useState, useContext, useEffect } from "react";
import { Layout, Button, Form, Input, message, Tooltip, Select, Checkbox, Card, Typography, Space, Divider } from "antd";
import { post, get } from "../../axios"; // Import Axios
import { OnboardingContext } from "./OnboardingContext";
import { router } from "@inertiajs/react"; // Add this import
import { ExclamationCircleOutlined, UserAddOutlined, DeleteOutlined, InfoCircleOutlined, MailOutlined } from "@ant-design/icons";
const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

const OnboardingSeven = () => {
    const { handleNext } = useContext(OnboardingContext);
    const [invitations, setInvitations] = useState([
        {
            email: "",
            permission_ids: [],
            errors: {}
        }
    ]);
    const [permissions, setPermissions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [permissionsLoading, setPermissionsLoading] = useState(true);
    const [isContinueDisabled, setIsContinueDisabled] = useState(true);
    const [formErrors, setFormErrors] = useState(null);

    const MAX_INVITATIONS = 30;
    const SCROLL_AFTER = 5;

    // Regular expression for basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Fetch available permissions on component mount
    useEffect(() => {
        const fetchPermissions = async () => {
            try {
                const response = await get("invite-permissions");
                setPermissions(response.permissions);
            } catch (error) {
                console.error("Error fetching permissions:", error);
                message.error("Failed to load permissions");
            } finally {
                setPermissionsLoading(false);
            }
        };

        fetchPermissions();
    }, []);

    // Effect to check if at least one invitation is valid
    useEffect(() => {
        const hasAtLeastOneValidInvitation = invitations.some(
            (invitation) =>
                invitation.email.trim() !== "" &&
                emailRegex.test(invitation.email.trim()) &&
                invitation.permission_ids.length > 0 &&
                !invitation.errors.email &&
                !invitation.errors.permission_ids
        );
        setIsContinueDisabled(!hasAtLeastOneValidInvitation || loading || permissionsLoading);
    }, [invitations, loading, permissionsLoading]);

    const validateEmail = (email) => {
        if (email.trim() === "") return null;

        if (!emailRegex.test(email.trim())) {
            return "Invalid email format";
        }

        return null;
    };

    const validateInvitation = (invitation, index) => {
        const errors = {};

        if (invitation.email.trim() !== "") {
            const emailError = validateEmail(invitation.email);
            if (emailError) {
                errors.email = emailError;
            }

            // Check for duplicate emails
            const duplicateIndex = invitations.findIndex((inv, idx) =>
                idx !== index && inv.email.trim().toLowerCase() === invitation.email.trim().toLowerCase()
            );
            if (duplicateIndex !== -1) {
                errors.email = "This email is already added";
            }
        }

        if (invitation.email.trim() !== "" && invitation.permission_ids.length === 0) {
            errors.permission_ids = "Please select at least one permission";
        }

        return errors;
    };

    const handleAddInvitation = () => {
        if (invitations.length >= MAX_INVITATIONS) {
            message.warning(`You can only add up to ${MAX_INVITATIONS} invitations.`);
            return;
        }
        setInvitations([...invitations, {
            email: "",
            permission_ids: [],
            errors: {}
        }]);
    };

    const handleRemoveInvitation = (index) => {
        if (invitations.length <= 1) {
            message.warning("You must have at least one invitation");
            return;
        }
        const newInvitations = invitations.filter((_, idx) => idx !== index);
        setInvitations(newInvitations);
    };

    const handleInvitationChange = (index, field, value) => {
        const newInvitations = [...invitations];
        newInvitations[index][field] = value;

        // Clear errors for this field
        if (newInvitations[index].errors[field]) {
            newInvitations[index].errors = {
                ...newInvitations[index].errors,
                [field]: null
            };
        }

        setInvitations(newInvitations);

        // Clear form-level errors when user starts correcting inputs
        if (formErrors) {
            setFormErrors(null);
        }
    };

    const handleInvitationBlur = (index) => {
        const newInvitations = [...invitations];
        const invitation = newInvitations[index];

        // Validate the invitation
        const errors = validateInvitation(invitation, index);
        newInvitations[index].errors = errors;

        setInvitations(newInvitations);
    };

    const handlePermissionChange = (index, selectedPermissions) => {
        const newInvitations = [...invitations];
        newInvitations[index].permission_ids = selectedPermissions;

        // Clear permission errors
        if (newInvitations[index].errors.permission_ids) {
            newInvitations[index].errors = {
                ...newInvitations[index].errors,
                permission_ids: null
            };
        }

        setInvitations(newInvitations);
    };

    const handleSubmit = async () => {
        // Reset form errors
        setFormErrors(null);

        // Validate all invitations before submission
        const newInvitations = [...invitations];
        let hasValidationErrors = false;

        newInvitations.forEach((invitation, index) => {
            const errors = validateInvitation(invitation, index);
            newInvitations[index].errors = errors;
            if (Object.keys(errors).length > 0) {
                hasValidationErrors = true;
            }
        });

        setInvitations(newInvitations);

        if (hasValidationErrors) {
            setFormErrors("Please fix the errors before continuing");
            return;
        }

        // Filter out empty invitations and prepare data
        const validInvitations = invitations.filter((invitation) =>
            invitation.email.trim() !== "" && invitation.permission_ids.length > 0
        );

        if (validInvitations.length === 0) {
            setFormErrors("Please enter at least one valid invitation");
            return;
        }

        setLoading(true);

        try {
            const response = await post("invite", { invitations: validInvitations });
            message.success("Invitations sent successfully!");

            // Add a small delay before navigation to show the success message
            setTimeout(() => {
                router.visit("/syncproducts");
            }, 1000);
        } catch (error) {
            console.error("Error sending invitations:", error);

            // Handle different error responses
            if (error.response) {
                const status = error.response.status;

                if (status === 422 && error.response.data && error.response.data.errors) {
                    // Handle validation errors returned from server
                    const serverErrors = error.response.data.errors;
                    const newInvitations = [...invitations];

                    // Map server errors to invitation fields
                    Object.keys(serverErrors).forEach(errorKey => {
                        const match = errorKey.match(/invitations\.(\d+)\.(.+)/);
                        if (match) {
                            const invitationIndex = parseInt(match[1]);
                            const fieldName = match[2];
                            if (newInvitations[invitationIndex]) {
                                newInvitations[invitationIndex].errors = {
                                    ...newInvitations[invitationIndex].errors,
                                    [fieldName]: serverErrors[errorKey][0]
                                };
                            }
                        }
                    });

                    setInvitations(newInvitations);

                    // Set general form error
                    if (error.response.data.message) {
                        setFormErrors(error.response.data.message);
                    } else {
                        setFormErrors("Some invitations could not be processed. Please check the errors and try again.");
                    }
                } else if (status === 401) {
                    setFormErrors("You are not authorized to send invitations");
                    message.error("Authentication error. Please log in again.");
                } else if (status === 429) {
                    setFormErrors("Too many invitation attempts. Please try again later.");
                } else if (error.response.data && error.response.data.message) {
                    setFormErrors(error.response.data.message);
                    message.error(`Error: ${error.response.data.message}`);
                } else {
                    setFormErrors("Server error. Please try again later.");
                    message.error("Failed to send invitations. Please try again.");
                }
            } else if (error.request) {
                // Network error
                setFormErrors("Network error. Please check your connection and try again.");
                message.error("Network error. Please check your connection.");
            } else {
                setFormErrors("An unexpected error occurred");
                message.error("Failed to send invitations. Please try again.");
            }
        } finally {
            setLoading(false);
        }
    };

    if (permissionsLoading) {
        return (
            <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#740898] mx-auto mb-4"></div>
                    <p className="text-gray-500">Loading permissions...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">Invite Members to your team</h2>
            <p className="text-gray-500 text-center mb-6">Enter team member details and assign permissions</p>

            <div className="max-w-4xl mx-auto">
                {formErrors && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-600 flex items-center">
                        <ExclamationCircleOutlined className="mr-3 text-lg" />
                        <span>{formErrors}</span>
                    </div>
                )}

                <div
                    className={`
                        ${invitations.length > SCROLL_AFTER ?
                            "max-h-[600px] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100" :
                            ""
                        }
                        space-y-4
                    `}
                >
                    {invitations.map((invitation, index) => (
                        <Card
                            key={index}
                            className="mb-4 shadow-sm hover:shadow-md transition-shadow duration-300"
                            headStyle={{ background: '#f9fafb', borderBottom: '1px solid #eaeaea' }}
                            bodyStyle={{ padding: '24px' }}
                            title={
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#f0e6f7] text-[#740898] mr-3">
                                            <UserAddOutlined />
                                        </div>
                                        <span className="font-medium">Team Member {index + 1}</span>
                                    </div>
                                    {invitations.length > 1 && (
                                        <Button
                                            type="text"
                                            danger
                                            size="small"
                                            icon={<DeleteOutlined />}
                                            onClick={() => handleRemoveInvitation(index)}
                                            className="hover:bg-red-50"
                                        >
                                            Remove
                                        </Button>
                                    )}
                                </div>
                            }
                        >
                            <div className="space-y-6">
                                {/* Email Information */}
                                <div>
                                    <Form.Item
                                        label={<span className="font-medium text-gray-700">Email Address</span>}
                                        required
                                        validateStatus={invitation.errors.email ? "error" : ""}
                                        help={invitation.errors.email}
                                        className="mb-0"
                                    >
                                        <Input
                                            value={invitation.email}
                                            onChange={(e) => handleInvitationChange(index, 'email', e.target.value)}
                                            onBlur={() => handleInvitationBlur(index)}
                                            placeholder="<EMAIL>"
                                            className={`rounded-md ${invitation.errors.email ? "border-red-500" : ""}`}
                                            prefix={<MailOutlined className="text-gray-400 mr-2" />}
                                            size="large"
                                        />
                                    </Form.Item>
                                </div>

                                {/* Permissions */}
                                <div>
                                    <div className="flex items-center mb-3">
                                        <span className="font-medium text-gray-700 mr-2">Permissions</span>
                                        <Tooltip title="Select the permissions this team member will have access to">
                                            <InfoCircleOutlined className="text-gray-400" />
                                        </Tooltip>
                                    </div>

                                    <Form.Item
                                        validateStatus={invitation.errors.permission_ids ? "error" : ""}
                                        help={invitation.errors.permission_ids}
                                        className="mb-0"
                                    >
                                        <Select
                                            mode="multiple"
                                            value={invitation.permission_ids}
                                            onChange={(value) => handlePermissionChange(index, value)}
                                            placeholder="Select permissions"
                                            className={`w-full rounded-md ${invitation.errors.permission_ids ? "border-red-500" : ""}`}
                                            optionLabelProp="label"
                                            size="large"
                                            maxTagCount={3}
                                            listHeight={250}
                                        >
                                            {permissions.map((permission) => (
                                                <Option
                                                    key={permission.id}
                                                    value={permission.id}
                                                    label={permission.name}
                                                >
                                                    <div className="flex flex-col py-1">
                                                        <span className="font-medium">{permission.name}</span>
                                                        <span className="text-xs text-gray-500">{permission.handle}</span>
                                                    </div>
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>

                                    {invitation.permission_ids.length > 0 && (
                                        <div className="mt-2">
                                            <Text className="text-xs text-gray-500">
                                                Selected: {invitation.permission_ids.length} permission{invitation.permission_ids.length !== 1 ? 's' : ''}
                                            </Text>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>

                <div className="flex justify-center mt-6">
                    <Button
                        type="dashed"
                        onClick={handleAddInvitation}
                        disabled={invitations.length >= MAX_INVITATIONS}
                        icon={<UserAddOutlined />}
                        className="border-[#740898] text-[#740898] hover:border-[#740898] hover:text-[#740898] h-12 px-6 rounded-md"
                        size="large"
                    >
                        <span className="ml-2">Add Another Team Member</span>
                    </Button>
                </div>

                {invitations.length >= MAX_INVITATIONS && (
                    <div className="text-center mt-3">
                        <Text className="text-red-500 text-sm flex items-center justify-center">
                            <ExclamationCircleOutlined className="mr-1" />
                            You have reached the maximum of {MAX_INVITATIONS} invitations.
                        </Text>
                    </div>
                )}

                <Divider className="my-8" />

                <div className="flex justify-center gap-6">
                    <Button
                        type="primary"
                        className="bg-[#740898] rounded-md font-medium h-12 px-8 border border-[#740898] text-white hover:bg-[#5f0679] hover:border-[#5f0679] flex items-center"
                        onClick={handleSubmit}
                        loading={loading}
                        disabled={isContinueDisabled}
                        icon={<UserAddOutlined />}
                        size="large"
                    >
                        <span className="ml-2">Send Invitations</span>
                    </Button>
                    <Button
                        type="text"
                        className="text-[#740898] h-12 px-8 hover:bg-[#f0e6f7] rounded-md font-medium"
                        onClick={() => router.visit("/syncproducts")}
                        disabled={loading}
                        size="large"
                    >
                        Skip for now
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default OnboardingSeven;
