import React from "react";
import { Modal, Form, Input, Button } from "antd";

const ShopifyUrlModal = ({ isVisible, onClose, onConnect, shopifyUrl, onShopifyUrlChange }) => {
    return (
        <Modal
            centered
            title="Enter your store URL"
            className="shopify-url-modal"
            open={isVisible}
            onCancel={onClose}
            footer={null}
            width={480}
            closable={false}
            maskClosable={true}
            style={{
                borderRadius: '8px',
            }}
        >
            <div className="space-y-4">
                <Form layout="vertical" className="mt-4">
                    <Form.Item 
                        label={<span className="text-[14px] font-normal text-[#252525]">Shop url</span>}
                        className="mb-1"
                    >
                        <Input
                            value={shopifyUrl}
                            onChange={(e) => onShopifyUrlChange(e.target.value)}
                            placeholder="your-shop-url.myshopify.com"
                            className="h-[32px] rounded-[4px] border-[#D9D9D9]"
                        />
                        <p className="text-[12px] font-normal text-[#626262] mt-1">
                            Haven't created a store yet? Learn more on{' '}
                            <a 
                                href="https://shopify.com" 
                                target="_blank" 
                                className="text-[#740898] hover:text-[#740898] underline"
                                rel="noopener noreferrer"
                            >
                                shopify.com
                            </a>
                        </p>
                    </Form.Item>
                    <div className="flex justify-end gap-2 mt-6">
                        <Button
                            onClick={onClose}
                            className="h-[32px] px-4 border border-[#D9D9D9] text-[#252525] hover:text-[#252525] rounded-[4px]"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={onConnect}
                            disabled={!shopifyUrl}
                            className="h-[32px] px-4 bg-[#740898] border-[#740898] hover:bg-[#740898] text-white rounded-[4px]"
                        >
                            Connect
                        </Button>
                    </div>
                </Form>
            </div>
        </Modal>
    );
};

export default ShopifyUrlModal; 