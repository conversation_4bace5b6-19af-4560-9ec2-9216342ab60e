// src/components/UnitSelect.jsx
import React from "react";
import { Select } from "antd";
import { units } from "./WeightUnitOptions";

const { Option } = Select;

/**
 * Controlled units dropdown.
 * Props:
 *  - value, onChange      // from Form.Item
 *  - ...rest              // any other Select props
 */
export default function UnitSelect({ value, onChange, ...rest }) {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="Select a unit"
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) =>
        option.children.toLowerCase().includes(input.toLowerCase())
      }
      {...rest}
    >
      {units.map(({ value: code, label }) => (
        <Option key={code} value={code}>
          {label}
        </Option>
      ))}
    </Select>
  );
}
