import React, { useState } from "react";
import { Row, Col, Button } from "antd";
import UpArrow from "../../../../public/v2/icons/uparrow-icon.svg";
import ShopifySync from "../../../../public/v2/icons/shopifysync-icon.svg";
import Info from "../../../../public/v2/icons/info-icon.svg";
import DoughnutChart from "../components/DoughnutChart";
import { router } from "@inertiajs/react";
import ShopifyUrlModal from "../../components/ShopifyUrlModal";
import ShopifyStepTwo from "../../components/ShopifyStepTwo";

const DashboardShopifySync = () => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [shopifyUrl, setShopifyUrl] = useState("");
    const [showStepTwo, setShowStepTwo] = useState(false);

    const handleConnect = () => {
        // Close the modal and show step two
        setIsModalVisible(false);
        setShowStepTwo(true);
    };

    const handleSkipStepTwo = () => {
        // Return to dashboard when user skips
        setShowStepTwo(false);
        router.visit("/dashboard");
    };

    const cards = [
        {
            index: 1,
            value: 60,
            quality: "Good",
            description: "Products with 90% or Greater Quality Score.",
            buttonLabel: "Update",
        },
    ];
    // If step two should be shown, render the ShopifyStepTwo component
    if (showStepTwo) {
        return <ShopifyStepTwo shopifyUrl={shopifyUrl} onSkip={handleSkipStepTwo} />;
    }

    return (
        <div className="p-5">
            <p className="text-[#252525] font-[600] text-[18px]">Shopify sync status</p>
            <p className="text-[#626262] font-normal text-[14px]">Shopify not synced</p>
            <div className="py-5">
                <Row className="flex gap-5">
                    <Col xs={5} xl={5}>
                        <img src={ShopifySync} alt="shopify sync" />
                    </Col>
                    {cards.map((card) => (
                        <Col md={18} xl={18} key={card.index}>
                            <div className="h-[168px] p-5 text-center bg-white flex flex-col rounded-[12px] items-center justify-center border border-[#EBEBEB]">
                                <p className="text-[#252525] text-[40px] font-[700]">
                                    <img src={Info} alt="info icon" />
                                </p>

                                <p className="text-[#252525] font-normal text-[14px]">
                                    Sync your Shopify store with our PIM to streamline product management and boost efficiency.
                                </p>
                                <Button
                                    className="bg-[#740898] border border-[#740898] text-white font-normal"
                                    onClick={() => setIsModalVisible(true)}
                                >
                                    Sync Store Now
                                </Button>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>

            <ShopifyUrlModal
                isVisible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                onConnect={handleConnect}
                shopifyUrl={shopifyUrl}
                onShopifyUrlChange={setShopifyUrl}
            />
        </div>
    );
};

export default DashboardShopifySync;
