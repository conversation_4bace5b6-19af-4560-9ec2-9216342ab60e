<!-- The Modal -->

<?php $__env->startPush("header_scripts"); ?>
    <meta name="_token" content="<?php echo e(csrf_token()); ?>" />

<?php $__env->stopPush(); ?>
<style>
    #dropzone {
        overflow: auto;
        width: 100%;
        height: 648px;
        /*display: grid;*/
        /*grid-auto-columns: max-content;*/
        /*grid-auto-flow: dense;*/
        /*grid-gap: 15px;*/
        /*grid-template-columns: repeat(5, 1fr);*/
        border: none;
        position: relative;
    }
    .dz-button {
        /*margin-top: 254px;*/
    }
    .dz-image {
        height: 200px;
        width: 200px;
    }
    .dz-image img {
        height: 100%;
        width: 100%;
    }
    .dz-preview {
        height: 125px;
        margin-right: 26px;
    }
    .dropzone .dz-message .dz-button {
        margin-top: 11px;
    }
    .dropzone .dz-preview .dz-image {
        border-radius: 20px;
        overflow: hidden;
        width: 146px;
        height: 135px;
        position: relative;
        display: block;
        z-index: 10;
        object-fit: cover;
    }
    .dropzone .dz-preview.dz-image-preview {
        margin-bottom: 30px;
        margin-top: 45px;
    }
    .dropzone.dz-started .dz-message {
        display: block !important;

    }
    .dropzone .dz-message .dz-button {
        margin-top: 0px;
    }
    .dropzone  .dz-message {
        position: relative !important;
        height: 220px;
        border: 2px dashed #a5a5a5;
        cursor: pointer !important;
        border-radius: 8px;
        width: 50%;
        background: #fff;
        color: #222;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .dropzone .dz-message .dz-button{
        background: 0 0;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
        outline: inherit;
        font-size: 22px;
        font-weight: bold;
        color: #000;
    }
    .dropzone .dz-message .dz-button i{display:block;color:#2c4bff; font-size:50px;margin-bottom: 10px;}
    .dropzone .dz-message .dz-button span{
        display: block;
        font-size: 16px;
        font-weight: 500;
        margin-top: 5px;
    }

    /* .dz-message:focus {
        box-shadow: #3c4fe0 0 0 0 1.5px inset, rgba(45, 35, 66, .4) 0 2px 4px, rgba(45, 35, 66, .3) 0 7px 13px -3px, #3c4fe0 0 -3px 0 inset;
    } */

    /*.dz-message:hover {*/
    /*    box-shadow: rgba(45, 35, 66, .4) 0 4px 8px, rgba(45, 35, 66, .3) 0 7px 13px -3px, #3c4fe0 0 -3px 0 inset;*/
    /*    transform: translateY(-2px);*/
    /*}*/

    /* .dz-message:active {
        box-shadow: #3c4fe0 0 3px 7px inset;
        transform: translateY(2px);
    } */
    .selectedFolder{
        background-color: rgb(209, 209, 209);
    }

    .dropzone .dz-preview .dz-progress .dz-upload {
        background: #3DB042 !important;
    }
    .dropzone .dz-preview .dz-error-message {
        top: 155px !important;
        left: 59px !important;
    }
    /*.my-custom-class .progress-bar {*/
    /*    background-color: green;*/
    /*}*/
</style>

<div class="modal fade " id="upload_image" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered media_modal_width">
        <div class="modal-content ">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    Select From Gallery
                </h5>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body d-flex flex-column" style="position: relative;margin-bottom: 44px; ">

                <div class="d-flex  mb-3">


                    <div style="width:29%" class="d-flex">
                        <div style="width: 100%;">
                            <div class="col-12 col-xl-12 col-lg-3 ">
                                <div class="d-flex flex-row">
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="<?php echo e(request('q')); ?>" name="q"
                                               placeholder="Search"
                                               aria-label="Search by SKU" aria-describedby="search">
                                        <?php if(request()->has('q')): ?>
                                            <a href="<?php echo e(\Illuminate\Support\Facades\Request::url()); ?>"
                                               class="ripplelink btn search_btn_close">
                                                <i class="fa fa-close"></i>
                                            </a>
                                        <?php endif; ?>
                                        <div class="input-group-append">
                                            <button class="search" type="submit" id="search">
                                                <img src="<?php echo e(asset('media/retailer-dashboard/Search-2.png')); ?>" alt="">
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="height: 609px; overflow-y: scroll">
                                <h2 class="mt-4" style="font-weight: 500; margin-bottom: 5px; font-family: Poppins, sans-serif"> Folders: </h2>
                                <div class="first_Tree" id="first_Tree"></div>
                            </div>
                        </div>
                        <div>
                        <div class="vl"></div>
                        </div>
                    </div>
                    <div style="width: 100%;">



                            <form action="<?php echo e(route('file.store')); ?>" method="post" enctype="multipart/form-data" class="dropzone new_drop my-dropzone"
                                  id="dropzone">
                                <?php echo csrf_field(); ?>

                                <input type="hidden" id="folderId" name="folder_id" value="<?php echo e($folderId); ?>">
                                <input type="hidden" id="folderId" name="product_id" value="<?php echo e($productId); ?>">

                            </form>


                    </div>
                </div>

                <div class="modal-footer"
                     style="position: absolute; width: 100%; bottom: -41px; left: 0; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; padding-top: 18px; border-top: 1px solid #8C8C8C;">
                    <button  class="btn btn btn-dark-tertiary shadow btn_close " style="width: 120px"> Cancel</button>


                    <button type="button" class="btn btn-info d-none" id="submit-all" >Upload</button>

                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade image-upload" id="uplhoad_image" tabindex="-1" style="overflow: auto !important; " aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-width">
        <div class="modal-content image-upload-width">
            <div class="modal-header">
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    Upload Image
                </h5>
                <button type="button" class="close " data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
























        </div>
    </div>
</div>
<style>
    .media_modal_width {
        max-width: 73%;
        min-width: 70%;
        min-height: 100%;
        margin-top: 0;
    }

    .vl {
        border-left: 1px solid #8C8C8C;
        height: 648px;
        margin-right: 17px;
        margin-left: 10px;
    }

    .img-modal {
        min-width: 100%;
        max-width: 100%;

        min-height: 198.5px;
        max-height: 198.5px;
        object-fit: cover;

    }

    .image_gallery {
        display: grid;
        grid-template: repeat(3, 205px) / repeat(3, 300px);
        grid-gap: 10px;
        justify-content: center;
        grid-column: 2 / span 3;
        grid-row: 3 / span 1;
        max-height: 645px;
        overflow: auto;
    }

    .img_caption {
        font-size: .9em;
        position: absolute;
        display: block;
        width: 100%;
        bottom: 0;
        padding: 0.6rem 0.8rem;
        color: #fff;
        background: linear-gradient(to left, rgba(85, 67, 70, 0.65), rgba(69, 80, 91, 0.65)) !important;


        opacity: 1;
        cursor: pointer;
    }

    .img_caption_info {
        position: absolute;
        font-size: .9em;

    }

    .img_hover {
        transform: scale(1);
        transition: .3s ease-in-out;
        cursor: pointer;

    }

    .img_hover:hover {
        transform: scale(1.2);

    }

    .label_css {
        border-radius: 0.25rem !important;

    }

    .label_css:hover {
        border-radius: 0.25rem !important;
    }

    .img_hover:hover label {
        border-radius: 0.25rem !important;

    }

    .label_css {
        margin-bottom: 0;
        font-weight: 400;
    }

    
    .jsTree {
        width: 97%;
    }

    .jsTree .itemParent { /* div. more down under */
        transition: all 0.3s ease-in;
        padding: 6px 0px;
        display: flex;
    }

    .jsTree .itemParent:hover {
        background-color: rgb(209, 209, 209);
    }

    .jsTree .itemParent .contenteditable {
        margin: 0px;
        flex-grow: 1;
    }

    .jsTree .itemParent p {
        margin: 0px 6px;
        max-width: 300px;
        padding: 2px 0px;
    }

    .jsTree .itemParent .afterIcon {
        display: inline-block;
        flex-shrink: 0;
        width: 15px;
        height: 19px;
        margin: 0px 11px;
        margin-top: 5px;
        background-size: 12px 12px;
        background-repeat: no-repeat;
        background-position: center center;
        cursor: pointer;
        transition: opacity 0.3s ease-out;
        opacity: 1;
        background: url(<?php echo e(asset('img/gallery/icons/arrow-down.svg')); ?>);

    }

    .jsTree .itemParent .afterIcon:hover .afterIcon {
        opacity: 1;
    }

    .jsTree .itemParent .afterIcon.arrowRotate {
        transform: rotate(-90deg);
    }

    .jsTree .childGroup { /* ul */
        padding: 0px 0px 0px 12px;
        margin: 0;
    }

    .jsTree .item { /* li */
        list-style: none;
        padding: 0;
        margin: 0;
        transition: all 0.3s ease-in;
    }

    .color {
        background: rgba(204, 204, 204, 0.8);
    }

    .jsTree .itemParent .preIcon {
        display: inline-block;
        flex-shrink: 0;
        width: 19px;
        height: 19px;
        margin: 0px 4px;
        background-size: 14px 14px !important;
        background-repeat: no-repeat !important;
        background-position: center center !important;
        font-size: 18px;
        margin-top: 4px
    }

    .jsTree .itemParent .preIcon.arrowDown {
        cursor: pointer;
        
                background: url(<?php echo e(asset('img/gallery/icons/folder-solid.svg')); ?>);

        transition: transform 0.3s ease-out;
    }

    .jsTree .itemParent .preIcon.arrowDown.arrowRotate {
        transform: rotate(-90deg);
    }


    .jsTreeContextMenu {
        width: -webkit-max-content;
        width: -moz-max-content;
        width: max-content;
        display: none;
        position: fixed;
        border-radius: 1px;
        overflow: hidden;
        background: white;
        border: 1px solid rgb(16, 111, 171);
        box-sizing: border-box;
    }

    .jsTreeContextMenu p {
        margin: 0;
        padding: 4px 8px;
        transition: all 0.3s ease-in;
        background: white;
    }

    .jsTreeContextMenu p:hover {
        background: #eee;
    }

    /*# sourceMappingURL=tree_editor.css.map */
    .first_Tree > * {
        font-family: Poppins, sans-serif;
    }
</style>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>


<script>
    class TreeEditor {
        mainNode;     //selector
        uuid;
        contextMenu;  //jQuery node
        selectedItem; //jQuery node
        options;      //{}

        //TODO add max capacity and tags, stackabel=bool
        constructor(mainNode) {

            this.uuid = this.newuuid();
            if (!$(mainNode).hasClass("jsTree"))
                $(mainNode).addClass("jsTree");//make sure styles are accurate

            $(mainNode).attr("ui-uuid", this.uuid);
            this.mainNode = mainNode + "[ui-uuid='" + this.uuid + "']";

            //TODO translte contextmenu items
            this.contextMenu = $("<div class='jsTreeContextMenu' ui-uuid='" + this.uuid + "'><p>Delete</p></div>");//<p>Move up</p><p>Move down</p>
            this.contextMenu.insertAfter($(this.mainNode));

            //one-off listeners:

            let jsTree = this;
            $(document).on("mousedown", function (e) {

                if (!$(e.target).hasClass("afterIcon") && !$(e.target).hasClass("jsTreeContextMenu") && !($(e.target).parents(".jsTreeContextMenu").length > 0)) {
                    jsTree.contextMenu.hide();
                }
            });

            //initial options:
            this.options = {
                checkboxes: false,
                radios: false,
                editable: true
            };
            //this.rebindListeners();
        }

        setData(data) {
            $(this.mainNode).empty();
            data.forEach(element => this.addElement(element, $(this.mainNode)));
            //TODO Optimize this line here
            this.rebindListeners();
            return this;
        }

        set(opts) {
            let jsTree = this;
            jsTree.options = opts;
            if (opts.extended === false) {
                $(this.mainNode + " .afterIcon").each(function () {
                    if ($(this).hasClass("arrowDown")) $(this).addClass("arrowRotate");
                });
                $(this.mainNode + " .childGroup").hide();
            }
            if (opts.checkboxes === true) {
                $(this.mainNode + " .afterIcon").each(function () {
                    $(this).removeClass("arrowDown");
                    $(this).addClass("checkboxIcon");
                });
                jsTree.options.radios = false;
            } else if (opts.radios === true) {
                $(this.mainNode + " .afterIcon").each(function () {
                    if (!$(this).hasClass("arrowDown")) {
                        $(this).addClass("radiobtnIcon");
                    }
                });
                jsTree.options.checkboxes = false;
            } else {
                jsTree.options.radios = false;
                jsTree.options.checkboxes = false;
            }

            if (opts.editable === false) {
                $(this.mainNode + " p").removeAttr("contenteditable");
                $(this.mainNode + " .afterIcon").hide();
            } else {
                jsTree.options.editable = true;

            }

            this.rebindListeners();
            return this;
        }

        getData() {
            let jsTree = this;
            folder_id = jsTree;
            var retVal = [];
            $(this.mainNode).subs().each(function () {
                jsTree.pushData(retVal, jsTree, $(this));
            });
            return retVal;
        }

        pushData(parentData, jsTree, subject) {
            if (subject.is("ul")) return;
            if (subject.is(".itemParent")) {
                let currentItem = {
                    title: subject.find("p").text()
                };
                if (subject.find(".afterIcon").hasClass("checked")) currentItem.checked = true;
                if (subject.next().is("ul")) {
                    currentItem.subs = [];
                    $(subject.next()).subs().each(function () {
                        jsTree.pushData(currentItem.subs, jsTree, $(this).find(".itemParent").eq(0));
                    });
                }

                parentData.push(currentItem);
            }
        }

        addElement(el, parentNode = null, id = null) {
            var $newNode;
            $(".childGroup").hide();
            if (parentNode.is("ul"))
                $newNode = $(`<li class='item' id='${el.id}'><div class='itemParent' id='${el.id}'>  </i><div class="contenteditable"><p class="text-truncate" contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B" class="text-truncate"  >${el.title}</p></div></span><span class='afterIcon' style="color: #5B5B5B"></span></div></li>`);
            else
                $newNode = $(`<div class='itemParent' id='${el.id}'><i class="fa-regular fa-folder preIcon" style="color: #5B5B5B"></i> </span><div class="contenteditable"><p contenteditable='false' style="font-family: Poppins, sans-serif; color: #5B5B5B" class="text-truncate">${el.title} </p></div></span> <span class='afterIcon' style="color: #5B5B5B"></span></div>`);
            //if(parentNode == null) parentNode = $(this.mainNode);
            parentNode.append($newNode);
            if (el.checked === true || el.checked === "true") $newNode.find(".afterIcon").addClass("checked");
            if (el.subs !== undefined) {
                $newNode.find(".afterIcon").addClass("arrowDown arrowRotate");
                var $chContainer = $("<ul class='childGroup'></ul>");
                if (parentNode.is("ul"))
                    $newNode.append($chContainer);
                else
                    $(this.mainNode).append($chContainer);
                el.subs.forEach(element => this.addElement(element, $chContainer));
            }
        }


//EVENT LISTENERS BEGIN HERE
        unbindListenders() {
            $(this.mainNode + " p").off();
            $(this.mainNode + " .preIcon").off();
            $(this.mainNode + " .afterIcon").off();
            $(".jsTreeContextMenu[ui-uuid='" + this.uuid + "'] p").off();
        }

        rebindListeners(jsTree = this) {
            jsTree.unbindListenders();
            $(this.mainNode + " p").keydown(function (e) {
                if (e.keyCode == 13) {//code here is duplicate from below
                    jsTree.selectedItem = $(":focus").closest(".itemParent");
                    if (jsTree.selectedItem.parent().is("li")) {

                    } else if (jsTree.selectedItem.next().length > 0 && jsTree.selectedItem.next().is(".childGroup")) {
                        $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                        $newNode.insertAfter(jsTree.selectedItem.next());
                    } else {
                        $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='afterIconEdit'></span><span class='afterIcon'></span></div>");
                        $newNode.insertAfter(jsTree.selectedItem);
                    }
                    jsTree.rerender(jsTree);
                    return false;
                }
            });


            $(this.mainNode + " p").on('blur', function () {
                jsTree.options.onchange(jsTree);
            });

            $(this.mainNode + " .afterIcon").on('click', function () {
                if ($(this).hasClass("arrowDown") && !$(this).hasClass("arrowRotate")) { //subs are expanded must retract
                    if ($(this).parent().parent().is("li"))
                        $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                    else
                        $(this).parent().next().animate({height: "toggle"}, 400);
                    $(this).addClass("arrowRotate");
                } else if ($(this).hasClass("arrowDown") && $(this).hasClass("arrowRotate")) { //subs are retracted
                    if ($(this).parent().parent().is("li"))
                        $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                    else
                        $(this).parent().next().animate({height: "toggle"}, 400);
                    $(this).removeClass("arrowRotate");
                } else if ($(this).hasClass("checkboxIcon")) {
                    if ($(this).hasClass("checked")) $(this).removeClass("checked");
                    else $(this).addClass("checked");
                } else if ($(this).hasClass("radiobtnIcon")) {
                    $(jsTree.mainNode + " .afterIcon").removeClass("checked");
                    $(this).addClass("checked");
                }

                if ($(this).hasClass("checkboxIcon") || $(this).hasClass("radiobtnIcon")) {
                    if (jsTree.options.oncheck !== undefined) {
                        let pathToDis = [];
                        var curItem = $(this).parent();
                        while (curItem.parent().is("li") || curItem.parent().is(jsTree.mainNode)) {
                            pathToDis.unshift(curItem.find("p").text());
                            curItem = curItem.parent().parent().prevAll().eq(0);
                        }
                        jsTree.options.oncheck($(this).hasClass("checked"), $(this).parent().find("p").text(), pathToDis);
                    }
                    if (jsTree.options.onchange !== undefined) {
                        jsTree.options.onchange(jsTree);
                    }
                }
            });


            var value
            $(this.mainNode + " .preIcon").on('click', function () {
                value = $(this).parents('.itemParent').attr('id')
                $(`#${value}`).toggleClass('color');
            });

        }

        rerender(jsTree = this) {
            if (jsTree.options.checkboxes === true) {
                $(jsTree.mainNode + " .afterIcon").each(function () {
                    if (!$(this).hasClass("arrowDown")) {
                        $(this).addClass("checkboxIcon");
                    }
                });
                jsTree.options.radios = false;
            } else if (jsTree.options.radios === true) {
                $(jsTree.mainNode + " .afterIcon").each(function () {
                    if (!$(this).hasClass("arrowDown")) {
                        $(this).addClass("radiobtnIcon");
                    }
                });
            } else {
                $(jsTree.mainNode + " .itemParent").each(function () {//TODO optimize, when delete delay required, otherwise not
                    if ($(this).next().is("ul")) {
                        if ($(this).next().subs().length > 0) {
                            $(this).find(".afterIcon").eq(0).addClass("arrowDown");
                            if ($(this).next().is(":visible")) {
                                $(this).find(".afterIcon").eq(0).removeClass("arrowRotate");
                            } else
                                $(this).find(".afterIcon").eq(0).addClass("arrowRotate");
                        } else
                            $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
                    } else
                        $(this).find(".afterIcon").eq(0).removeClass("arrowDown");
                });
            }
            if (jsTree.options.onchange !== undefined) {
                jsTree.options.onchange(jsTree);
            }

            jsTree.rebindListeners(jsTree);
        }

        newuuid() {
            return ([1e7] + -1e11).replace(/[018]/g, c =>
                (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
            );
        }
    }
    let folder_data = <?php echo json_encode($folders_list, 15, 512) ?>;
    console.log(folder_data)
    $(".first_Tree").each(function( index ) {
        const divIdAttribute=$('.first_Tree')[index];
        const divId= $(divIdAttribute).attr('id');

        new TreeEditor(`#${divId}`).setData(folder_data);
    });

    $( document ).ready(function() {
        let folder_Id;
        $('.contenteditable').on('click',function () {
            const clickedFolder = $(this).parents('.itemParent');
            const allFolders = $('.itemParent');
            // Remove 'selectedFolder' class from other folders
            allFolders.not(clickedFolder).removeClass('selectedFolder');

            folder_Id = clickedFolder.attr('id');
            clickedFolder.addClass('selectedFolder');
            // clickedFolder.toggleClass('selectedFolder');

            if(clickedFolder.hasClass('selectedFolder')){
                $('#folderId').val(folder_Id);
                // load_data();
            }
        });
    });




    
    let folder_div = document.querySelector(".modal-folder-section")
    let sub_folder_div = document.querySelector(".sub-folder-section")
    let go_back = document.querySelector(".back-link")
    let browse_img =  document.querySelector(".dz-button")
    //
    // $('#upload_image').on('hidden.bs.modal', function (e) {
    //     $(this).find('form')[0].reset();
    // });

</script>
<script type="text/javascript">
    // $('#upload_image').on('hidden.bs.modal', function () {
    //     $(this).find('form')[0].reset();
    // });
 
    Dropzone.options.dropzone = {
        autoProcessQueue : true,
        dictDefaultMessage: "<i class='icon fa-regular fa-images'></i>Drag and Drop a .jpg,.webp, file<span>or click to browse your files</span>",
        addRemoveLinks: false,
        acceptedFiles : ".jpg, .webp",
        maxFiles: 100,
        maxFilesize: 5, // in MB

        init:function(){

            var submitButton = document.querySelector("#submit-all");
            myDropzone = this;

            submitButton.addEventListener('click', function(){
                // myDropzone.processQueue();
                let acceptedFiles = myDropzone.getAcceptedFiles();
                for (let i = 0; i < acceptedFiles.length; i++) {
                    setTimeout(function () {
                        myDropzone.processFile(acceptedFiles[i])
                        file.previewElement.getElementsByClassName("progress-bar")[i].style.backgroundColor = "green";
                    }, i * 10)
                }
            });

            $('#close_button').click(function() {
                Dropzone.forElement("#dropzone").removeAllFiles(true);
            });
            $('.btn_close').click(function () {
                $('#upload_image').modal('hide');
                Dropzone.forElement("#dropzone").removeAllFiles(true);
                $(this).find('form')[0].reset();
            });
            var totalFiles = 0;
            var acceptedFiles = 0;
            var errorfiles = 0;
            var errorTooltip;
            this.on("addedfile", function(file) {
                let dzButton = document.querySelector(".dz-button");
                totalFiles++;

                if(totalFiles > 0) {
                    dzButton.style.marginTop = "9.5px";
                }


            });
            this.on("thumbnail", function(file) {
                // acceptedFiles++;
                // console.log(acceptedFiles, "new")
                // if (acceptedFiles === totalFiles) {
                //     // submitButton.removeAttribute("disabled");
                //     // errorTooltip.tooltip("hide");
                // }
            });
            this.on("sending", function(file) {
                var progressBar = file.previewElement.getElementsByClassName("dz-processing")[0];
                if (progressBar) {
                    progressBar.style.background = "green";
                }
            });
            // this.on("removedfile", function(file) {
            //     let dzButton = document.querySelector(".dz-button");
            //     console.log("dff")
            //     console.log(totalFiles, "removed")
            //     totalFiles--
            //     if (totalFiles == 0) {
            //         dzButton.style.marginTop = "0";
            //     }
            //     if (file.status === "error") {
            //
            //         errorfiles--
            //         if (errorfiles === 0) {
            //             submitButton.removeAttribute("disabled");
            //             errorTooltip.tooltip("hide");
            //         }
            //     }
            //
            // });
            this.on("complete", function(file){
                // file.previewElement.querySelector("[data-dz-remove]").innerHTML = "";
                if(this.getQueuedFiles().length == 0 && this.getUploadingFiles().length == 0)
                {
                    location.reload();
                    // var _this = this;
                    // $('#upload_image').modal('hide');

                    // _this.removeAllFiles();

                }
                // load_images();
              else   if (file.status == "error") {

                    errorfiles++
                    // Display error message to user
                    //  errorTooltip = $('#submit-all').tooltip({
                    //     title: "Unsupported file type.",
                    //     trigger: "manual"
                    // });
                    // file.previewElement.querySelector("[data-dz-remove]").innerHTML ="Remove file";
                    // submitButton.setAttribute("disabled", "disabled");
                    // errorTooltip.tooltip("show");
                } else {
                    // submitButton.removeAttribute("disabled");
                    // errorTooltip.tooltip("hide");
                        // $('#upload_image').modal('hide');
                        // var _this = this;

                    // _this.removeAllFiles();

                }
            });

        }

    };

    // load_images();

    function load_images()

    {

        $.ajax({
            method:'POST',
            url:"<?php echo e(route('file.store')); ?>",
            success:function(data)
            {
                $('#uploaded_image').html(data);
            }
        })
    }

</script>


<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\gallery\src\Providers/../views/components/image-uploader.blade.php ENDPATH**/ ?>