<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
<style>
</style>
<div id="folder-child" class=" d-flex folder-card folder_card_display ">
















































    <div class="folder d-flex justify-content-between align-items-center mb-2">
       <?php if((int)$id!=0): ?>
            <a href="<?php echo e(route('gallery.show',$id)); ?>" class="folder-icon-link d-flex text-decoration-none tooltip_name "
               data-trigger="hover"
               data-toggle="tooltip"
               data-placement="top"
               title=""
               style="width: 200px;" >

                <em class="fa-regular fa-folder mr-3 folder-icon align-self-center" style="color:#5B5B5B;"> </em>
                <p class="mb-0 flex-grow-1 ms-3 text-truncate folder_name "><?php echo e($name); ?></p>
            </a>
                <?php else: ?>
            <a href="<?php echo e(route('guests.show',$id)); ?>" class="folder-icon-link d-flex text-decoration-none tooltip_name "
               data-trigger="hover"
               data-toggle="tooltip"
               data-placement="top"
               title=""
               style="width: 200px;">
                <em class="fa-regular fa-folder mr-3 folder-icon align-self-center" style="color:#5B5B5B;"> </em>
                <p class="mb-0 flex-grow-1 ms-3 text-truncate folder_name"><?php echo e($name); ?></p>
            </a>
        <?php endif; ?>
        <div class="dropdown">
            <em class="fa-solid fa-download download-btn" data-bs-toggle="modal" data-bs-target="#download_subfolder" style="color:#5B5B5B;"></em>
            <a href="#"  class="text-decoration-none" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false" >

                <i class="icon icon-dots text-secondary"></i>

            </a>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink" style="border-radius: 15px; min-width: 9rem;">
                <li>
                    <?php if(!$folder->is_default): ?>
                                            <a class="dropdown-item"
                                               role="button"
                                               data-id="<?php echo e($id); ?>"
                                               data-name="<?php echo e($name); ?>"
                                               data-link="<?php echo e(route('gallery.store')); ?>"
                                               data-bs-toggle="modal"
                                               data-bs-target="#rename-button-<?php echo e($id); ?>"
                                            style="font-weight: 300; padding: 10px;  font-size: 16px; ">Rename</a>
                                        <?php endif; ?>
                </li>
                <li>
                    <a class="dropdown-item"
                       data-bs-toggle="modal"
                       data-bs-target="#share-folder-<?php echo e($id); ?>"
                       style="font-weight: 300; padding: 10px;  font-size: 16px;" >Share Link</a></li>
                <li>
                    <?php if(!$folder->is_default): ?>
                    <a class="dropdown-item"
                       data-id="<?php echo e($id); ?>"

                       data-bs-toggle="modal"
                       data-bs-target="#remove_folder-<?php echo e($id); ?>"
                       style="font-weight: 300; padding: 10px;  font-size: 16px;"
                       href="#">Delete</a>
                    <?php endif; ?>

                </li>

            </ul>
        </div>
    </div>


</div>
<?php if (isset($component)) { $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\FolderCreate::resolve(['id' => 'download_subfolder','parentFolderId' => ''.e($id).'','header' => 'Email Address','formUrl' => ''.e(route('file.downloadZip')).'','title' => 'Email','btnText' => 'Submit','message' => 'Please enter your email and get your required folder mailed to you'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-folder-create'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\FolderCreate::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $attributes = $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $component = $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\FolderCreate::resolve(['id' => 'rename-button-'.e($id).'','header' => 'Update Folder','formUrl' => ''.e(route('gallery.store')).'','folderId' => ''.e($id).'','folderName' => ''.e($name).'','title' => 'Folder Name','message' => '','btnText' => 'Save','parentFolderId' => ''.e(isset($parentFolderId) ? $parentFolderId : null).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-folder-create'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\FolderCreate::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $attributes = $__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__attributesOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6)): ?>
<?php $component = $__componentOriginalce4a40a46691b978a395a9c4d91dd7b6; ?>
<?php unset($__componentOriginalce4a40a46691b978a395a9c4d91dd7b6); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginal59403c4dd3dba3a75b115755225846f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal59403c4dd3dba3a75b115755225846f6 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\ShareFolder::resolve(['id' => 'share-folder-'.e($id).'','shareLink' => ''.e($link).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-share-folder'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\ShareFolder::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['folderId' => ''.e($id).'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal59403c4dd3dba3a75b115755225846f6)): ?>
<?php $attributes = $__attributesOriginal59403c4dd3dba3a75b115755225846f6; ?>
<?php unset($__attributesOriginal59403c4dd3dba3a75b115755225846f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal59403c4dd3dba3a75b115755225846f6)): ?>
<?php $component = $__componentOriginal59403c4dd3dba3a75b115755225846f6; ?>
<?php unset($__componentOriginal59403c4dd3dba3a75b115755225846f6); ?>
<?php endif; ?>

<?php if (isset($component)) { $__componentOriginal283be3cc056b9b5232cd8220545a7297 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283be3cc056b9b5232cd8220545a7297 = $attributes; } ?>
<?php $component = Apimio\Gallery\components\RemoveModal::resolve(['id' => 'remove_folder-'.e($id).'','formUrl' => ''.e(route('gallery.destroy',is_int($id)?$id:decrypt($id))).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('gallery-remove-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Apimio\Gallery\components\RemoveModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283be3cc056b9b5232cd8220545a7297)): ?>
<?php $attributes = $__attributesOriginal283be3cc056b9b5232cd8220545a7297; ?>
<?php unset($__attributesOriginal283be3cc056b9b5232cd8220545a7297); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283be3cc056b9b5232cd8220545a7297)): ?>
<?php $component = $__componentOriginal283be3cc056b9b5232cd8220545a7297; ?>
<?php unset($__componentOriginal283be3cc056b9b5232cd8220545a7297); ?>
<?php endif; ?>


<?php $__env->startPush('footer_scripts'); ?>

    <script>

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>

    <script>
        $('.rename-button-<?php echo e($id); ?>').on('click', function () {
            $('#folder-name').attr('placeholder', $(this).data('name'));
            $('#folder-name').attr('value', $(this).data('name'));
            $('#rename-form').attr('action', $(this).data('link'));
            $('#id').attr('value', $(this).data('id'));
        });

    </script>
<?php $__env->stopPush(); ?>






<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\gallery\src\Providers/../views/components/folder.blade.php ENDPATH**/ ?>