<style>
    .disabled-link {
        opacity: 0.5;
    }
</style>

<div class="col-auto col-md-3 col-xl-2 px-0 position-relative main-sidebar px-2 ">
    <div class="d-flex flex-column align-items-start align-items-sm-start text-white min-vh-100">
        <a href="/" class="my-4 d-flex justify-content-center mobile-logo">
            <img id="logo_sidebar" src="<?php echo e(asset('assets/images/apimio-small.svg')); ?>" class="mb-16 d-block d-sm-inline"
                alt="logo" />
        </a>
        <a href="/" class="text-decoration-none my-1 first-latter">
            <span class="circle bg-warning"><?php echo e(strtoupper(Auth::user()->fname[0])); ?></span>
        </a>

        <ul class="nav nav-pills flex-column mb-sm-auto mb-0 w-100 pt-4" id="menu">
            <li class="nav-item pl-12">
                <a href="<?php echo e(route('dashboard')); ?>" class="nav-link align-middle px-0 ">
                    <span class="icon icon-category" title="Dashboard"></span>
                    <span class="ms-4 d-none d-sm-inline">Dashboard</span>
                </a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'product')): ?>
                <li class="nav-item pl-12 active-link">
                    <a href="#" class="nav-link align-middle px-0 active-link product-link">
                        <span class="icon icon-product " title="Product"></span>
                        <span class="ms-4 d-none d-sm-inline">Product</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-product" title="Product"></span>
                            <span class="ms-3 d-none d-sm-inline">Product</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'gallery')): ?>
                <li class="nav-item pl-12 <?php echo e(Request::segment(1) == 'gallery' ? 'active-link' : ''); ?>">
                    <a href="<?php echo e(route('gallery.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(1) == 'gallery' ? 'active-link' : (Request::segment(1) == 'gallery' ? 'active-link' : (Request::segment(1) == 'gallery' ? 'active-link' : ''))); ?>">
                        <i class="icon fa-regular fa-images" title="Gallery"></i>
                        <span class="ms-3 d-none d-sm-inline">Gallery</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <i class="icon fa-regular fa-images" title="Gallery"></i>
                            <span class="ms-3 d-none d-sm-inline">Gallery</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'gallery')): ?>
                <li class="nav-item pl-12 <?php echo e(Request::segment(1) == 'gallery' ? 'active-link' : ''); ?>">
                    <a href="/brandportal/create"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(1) == 'gallery' ? 'active-link' : (Request::segment(1) == 'gallery' ? 'active-link' : (Request::segment(1) == 'gallery' ? 'active-link' : ''))); ?>">
                        <i class="icon fa-regular fa-solid fa-globe"></i>

                        
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="/brandportal/create" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <i class=" icon fa-regular fa-solid fa-globe"></i>

                            
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            
            
            
            
            
            
            
            
            
            
            
            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'invite-team')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('organization.invite_team.index')); ?>" class="nav-link align-middle px-0">
                        <span class="icon icon-teams" title="Invite Team"></span>
                        <span class="ms-4 d-none d-sm-inline">Invite Team</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-teams" title="Invite Team"></span>
                            <span class="ms-3 d-none d-sm-inline">Invite Team</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <li class="nav-item pl-12">
                <a href="<?php echo e(route('billing')); ?>" class="nav-link align-middle px-0">
                    <span class="icon icon-billing" title="Plans"></span>
                    <span class="ms-4 d-none d-sm-inline">Plans</span>
                </a>
            </li>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'notification')): ?>
                <li class="nav-item pl-12 position-relative">
                    <a href="<?php echo e(route('notification.index')); ?>" class="nav-link align-middle px-0">
                        <span class="icon icon-bell-regular" title="Notifications"></span>
                        <span class="ms-4 d-none d-sm-inline">Notifications</span>
                    </a>
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(auth()->user()->unreadNotifications->where('organization_id', auth()->user()->organization_id)->count() > 0): ?>
                            <span class="notification notification-danger me-2 sidebar-notification"></span>
                        <?php endif; ?>
                    <?php endif; ?>

                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-bell-regular" title="Notifications"></span>
                            <span class="ms-3 d-none d-sm-inline">Notifications</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <li class="nav-item pl-12">
                <a href="https://support.apimio.com/" target="_blank" class="nav-link align-middle px-0">
                    <span class="icon icon-help" title="Help"></span>
                    <span class="ms-4 d-none d-sm-inline">Help</span>
                </a>
            </li>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'settings')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('pages.settings')); ?>" class="nav-link align-middle px-0 text-decoration-none">
                        <span class="icon icon-setting" title="SETTINGS"></span>
                        <span class="ms-4 d-none d-sm-inline">SETTINGS</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-setting" title="SETTINGS"></span>
                            <span class="ms-3 d-none d-sm-inline">SETTINGS</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <li class="nav-item pl-12">
                <a href="/login.html" class="nav-link align-middle px-0 text-decoration-none"
                    onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                    aria-label="Logout" data-microtip-position="right" role="tooltip">
                    <span class="icon icon-logout" title="LOG OUT"></span>
                    <span class="ms-4 d-none d-sm-inline">LOG OUT</span>
                </a>
                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                    <?php echo csrf_field(); ?>
                </form>
            </li>
        </ul>
    </div>

    <div class="submenu min-vh-100 ">
        <ul class="nav nav-pills flex-column mb-sm-auto mb-0 w-100 pt-4" id="submenu_product">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'product')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('products.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(1) == 'products' && Request::segment(2) == null ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">All Products</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">All Products</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'import')): ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add_and_edit_product', [\App\Models\Organization\OrganizationUserPermission::class,
                    auth()->user()->organization_id])): ?>
                    <li class="nav-item pl-12">
                        <a href="<?php echo e(route('import.csv.step1')); ?>"
                            class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'import' ? 'active-product-link' : ''); ?>">
                            <span class="ms-2 d-sm-inline">Import</span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Import</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'export')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('export.exportOne')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'export' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Export</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Export</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'category')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('categories.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'categories' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Categories</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Categories</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'attribute-set')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('family.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'family' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Attribute Sets</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Attribute Sets</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'attribute')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('attributes.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'attributes' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Attributes</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Attributes</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'variant-option')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('variant_attribute.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'variant-attribute' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Variant Options</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Variant Options</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'brand')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('brands.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'brands' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Brands</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Brands</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'vendor')): ?>
                <li class="nav-item pl-12 <?php echo e(Request::segment(2) == 'vendors' ? 'active-link' : ''); ?>">
                    <a href="<?php echo e(route('vendors.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'vendors' ? 'active-link-product' : ''); ?>">
                        <span class="ms-2 d-none d-sm-inline">Vendors</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Vendors</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-lang', \App\Models\Product\Version::query())): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('versions.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'versions' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Languages</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Languages</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'location')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('locations.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(2) == 'locations' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Locations</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Locations</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', 'store')): ?>
                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('channel.index')); ?>"
                        class="nav-link align-middle px-0 <?php echo e(Request::segment(1) == 'channel' ? 'active-product-link' : ''); ?>">
                        <span class="ms-2 d-sm-inline">Stores</span>
                    </a>
                </li>
            <?php else: ?>
                <li class="nav-item pl-12">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0 disabled-link"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="ms-2 d-sm-inline">Stores</span>
                            <i class="fa fa-lock text-secondary me-4"></i>
                        </div>
                    </a>
                </li>
            <?php endif; ?>

                <li class="nav-item pl-12">
                    <a href="<?php echo e(route('tasks.index')); ?>" class="nav-link align-middle px-0 <?php echo e((Request::segment(2) == 'tasks')?'active-product-link':''); ?>">
                        <span class="ms-2 d-sm-inline">Price Schedules</span>
                    </a>
                </li>
        </ul>

        <?php ($org = \App\Models\Organization\Organization::where('id', \Illuminate\Support\Facades\Auth::user()->organization_id)->first()); ?>
        <?php if(isset($org) && $org->onTrial()): ?>
            
            <div class="mt-5 billing-timer">
                <div class="position-relative">
                    <div class="billing-custom-css mx-auto billing-custom-css_product d-none d-lg-block"
                        style="z-index: 1000;position: absolute">
                        <div>
                        </div>
                        <div class="mb-3 text-center">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                                <path
                                    d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                                    stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path
                                    d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
                                    stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                        <h3 class="text-center"><b>Trial Plan</b></h3>
                        <p class="mb-3 text-center">
                            <?php echo e(now()->diffInDays(\Carbon\Carbon::parse($org->trial_ends_at))); ?> Days left in your
                            trial.</p>
                        <div class="text-center"><a href="<?php echo e(route('billing')); ?>" class="btn-sm btn-danger">Click to
                                Upgrade </a></div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </div>

</div>
<?php $__env->startPush('footer_scripts'); ?>
    <script src="<?php echo e(asset('assets/js/mobile.js')); ?>"></script>
    <script>
        // Initialize Bootstrap tooltips with custom trigger option
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            var tooltip = new bootstrap.Tooltip(tooltipTriggerEl, {
                trigger: 'hover focus'
            });

            // Hide tooltip on click
            tooltipTriggerEl.addEventListener('click', function() {
                tooltip.hide();
            });

            return tooltip;
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/layouts/navs/product_sidebar.blade.php ENDPATH**/ ?>