<?php

namespace App;

use Apimio\Gallery\Models\File;
use App\Models\BrandsPortal;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Product\Attribute;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Version;
use App\Models\Location\Location;
use App\Notifications\CreateUserNotification;
use App\Services\Marketing\Hubspot\Hubspot;
use App\Traits\FreshSales\FreshSales;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Lara<PERSON>\Cashier\Billable;
use App\Traits\Billing\BillingTrait;
use Laravel\Sanctum\HasApiTokens;
use App\Models\Organization\TeamInvite;

class User extends Authenticatable implements MustVerifyEmail

{
    use Notifiable, BillingTrait, FreshSales, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'fname', 'lname', 'email', 'password', 'verification_code', 'phone', 'email_verified_at', 'shopify_shop_id','last_login'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public static function boot()
    {
        parent::boot();
        static::created(function ($model) {
            try
            {
                $ip = null;
                if (request()->has('ip'))
                { $ip = request()->ip; }
                    try{
                      $model->notify(new CreateUserNotification($model ,$ip));
                    }catch(\Exception $e){
                        Log::error($e);
                    }
                if ($model->shopify_shop_id || $model->google_id) {
                    //creating hubspot sales contact on the shopify  or google login
                    if(App::environment("production")) {
                        $hubspot = new Hubspot($model);
                        $hubspot->create();
                    }
                } else {
                    //creating hubspot sales contact on the manual login
                    if(App::environment("production")) {
                        $hubspot = new Hubspot($model);
                        $hubspot->notVerified()->create();
                    }
                }
            }
            catch(\Exception $e){
                Log::error($e);
            }
        });
    }

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function getOrganizationIdAttribute()
    {
        return session('organization_id', null);
    }

    public function routeNotificationForSlack() {
        return env('LOG_SLACK_WEBHOOK_URL');
    }

    public function getUserType():string
    {
        //type check
        if($this->google_id) {
            return 'google';
        }
        elseif($this->shopify_shop_id) {
            return 'shopify';
        }
        else {
            return 'manual';
        }
    }

    public function set_ip($ip){
        return $this->ip = $ip;
    }

    /**
     * Profile Update method
     *
     *
     */
    public function profile_update($request)
    {
        $user = Auth::user();
        $user->fname = $request['fname'];
        $user->lname = $request['lname'];
        $user->phone = $request['phone'];
        $user->save();
        return $user;
    }

    public function get_catalogs($organization)
    {
        return implode(", ", array_column($organization->channels_without_scope->toArray(), "name"));
    }

    /**
     * @param $organization
     * @return string
     */
    public function get_user_status($organization): string
    {
        $status = $organization->get_status();
        if ($status == 1) {
            $html= '<span class="badge badge-pill badge-success">Connected</span>';
        } else if ($status == 2) {
            $html= '<span class="badge badge-pill badge-danger">Declined</span>';
        } else if ($status == 3) {
            $html= '<span class="badge badge-pill badge-warning">Received</span>';
        } else if ($status == 5) {
            $html='<span class="badge badge-pill badge-danger">Disconnected</span>';
        }
        else{
            $html='<span class="badge badge-pill badge-primary">Sent</span>';
        }
        return $html;
    }

    public function get_status(): int
    {
        if ($this->is_accepted) {
            if($this->is_declined){
                return 5; //disconnected
            }
            else{
                return 1;// connected
            }
        } else if ($this->is_declined) {
            $status= 2; // declined
        } else if (Auth::user()->email == $this->email) {
            $status= 3; // received
        } else {
            $status= 4; // sent
        }
        return  $status;
    }


    public function get_organization_total_products(){
        return (Product::where('organization_id', $this->organization_id)->count());
    }

    /**
     * return object of the user organization
     * @return object
     */
    public function user_organization() {
        return Organization::where('id',$this->organization_id)->first();
    }

    // relationships
    public function organizations()
    {
        return $this->belongsToMany(Organization::class, 'organization_user');
    }


    public function categories()
    {
        return Category::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function brands()
    {
        return Brand::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function channels()
    {
        return Channel::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function versions()
    {
        return Version::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function attributes()
    {
        return Attribute::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function families()
    {
        return Family::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function files()
    {
        return File::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function products()
    {
        return Product::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }

    public function invites()
    {
        return Invite::withoutGlobalScopes()->where('organization_id_sender', $this->organization_id);
    }

    public function brandsPortals(){
        return BrandsPortal::withoutGlobalScopes()->where('organization_id', $this->organization_id);
    }
    public function teamInvites()
    {
        return $this->hasMany(TeamInvite::class, 'organization_id', 'organization_id')->withoutGlobalScopes();
    }

    public function locations()
    {
        return $this->hasMany(Location::class, 'organization_id', 'id')->withoutGlobalScopes();
    }
}
