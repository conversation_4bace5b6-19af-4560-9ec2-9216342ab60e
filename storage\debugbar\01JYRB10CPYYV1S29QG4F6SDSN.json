{"__meta": {"id": "01JYRB10CPYYV1S29QG4F6SDSN", "datetime": "2025-06-27 09:20:46", "utime": **********.999518, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.22863, "end": **********.999539, "duration": 0.7709088325500488, "duration_str": "771ms", "measures": [{"label": "Booting", "start": **********.22863, "relative_start": 0, "end": **********.949107, "relative_end": **********.949107, "duration": 0.****************, "duration_str": "720ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.94912, "relative_start": 0.****************, "end": **********.999541, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "50.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.968645, "relative_start": 0.****************, "end": **********.975986, "relative_end": **********.975986, "duration": 0.007340908050537109, "duration_str": "7.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.996716, "relative_start": 0.****************, "end": **********.997116, "relative_end": **********.997116, "duration": 0.0004000663757324219, "duration_str": "400μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1956\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#1685 …}\n  file: \"C:\\Users\\<USER>\\Desktop\\work\\apimio\\routes\\web.php\"\n  line: \"97 to 103\"\n}", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Froutes%2Fweb.php&line=97\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:97-103</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01JYRB0Z7MAE2HBK3Y86GAYQ7P\" => null\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000", "action_name": null, "controller_action": "Closure", "uri": "GET /", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Froutes%2Fweb.php&line=97\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:97-103</a>", "middleware": "web", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4104f2-aab2-4c85-8f42-7f9844cb4c4a\" target=\"_blank\">View in Telescope</a>", "duration": "778ms", "peak_memory": "30MB", "response": "Redirect to http://localhost:8000/register", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1354650519 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1354650519\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-205326937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-205326937\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-814600414 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"775 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; XSRF-TOKEN=eyJpdiI6IkdBWTRBa3VWcXEvZXhSc1lCcFdsM2c9PSIsInZhbHVlIjoiWGxRYTY3ZnNLenpUeVR1amczVmVDUFhzTXZibEdGeXVBSU1qRGF2ZzNyY3RDNXdCR0JRWjQrM0VENHRZMUt2T2svUktXbWFoY1h4dE9CaGFLL1BrUW9qditwOUtZMDhZc3dQV1lXcGUxTnY4bUJSOEs3VlRuQWE5alpQL1dJRlIiLCJtYWMiOiJiYTQ5YjZlMzI3OTJkMGY2NDE3NGJkZWM0N2ZkNWViNjZhMzQyOTgwZWNkZTFmNjM2NTBkNTMzZjk0YzRlYmEzIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjQ4VTVjV1pqUmNuUFZaWEdDZGVmRXc9PSIsInZhbHVlIjoibWJWaDEySUlYOHlKbjlKQUFFMFNvVFVLYThOc0h5OXAzM1JBeHlyaituVlFMZm9lbEoyMDJBMWt3K0RYTXRBSWpabE5xdlZ2VUpIZmo2dXJzZ1lpRlNydnh2WjJ1RlRpNkVkdk1tTUo4THBPYjVUVU5waTFrbThqUmxmT2tIamsiLCJtYWMiOiI0YWQyM2FkNjM0ZDE1N2FhMjViYzkyOGNiNDIyNTQyZGEzNDBkNDQwMTljMzY4ZTgyZDgyNDk5YzhmNGExMzg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814600414\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-317753234 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uTCWqRVeCOeDxGB3A1Z7rgLTYClC2xJgpr3CX5ri</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317753234\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:20:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost:8000/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1599220801 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JYRB0Z7MAE2HBK3Y86GAYQ7P</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599220801\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000", "controller_action": "Closure"}, "badge": "302 Found"}}