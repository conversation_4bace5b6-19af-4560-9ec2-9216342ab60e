<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Organization\TeamInvite;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Inertia\Response;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = 'onboarding';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('updateHubspotEmailVerification');
        $this->middleware('signed')->only('verify');
        $this->middleware('throttle:6,1')->only('verify', 'resend');
    }

    public function show(Request $request)
    {
        return $request->user()->hasVerifiedEmail()
            ? redirect($this->redirectPath())
            : redirect()->route('verify');
    }

    public function verifiedUserRedirect()
    {
        // Redirect if the user is already verified
        if (auth()->user()->hasVerifiedEmail()) {
            return redirect()->route('dashboard'); // Redirect to dashboard or a different page
        }
    }

    public function renderVerify(): Response
    {
        return Inertia::render('auth/Verify', [
            'user' => auth()->user(),
        ]);
    }

    /**
     * The user has verified their email address.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function verified(Request $request)
    {
        // Check if this was an invitation registration
        if (session('is_invitation_registration')) {
            session()->forget('is_invitation_registration');

            // Check for team invitation and handle accordingly
            $teamInvitation = $this->checkForTeamInvitation($request->user()->email);
            if ($teamInvitation) {
                return $this->handleTeamInvitationVerification($request->user(), $teamInvitation);
            }
        }

        // Default behavior for regular users
        return redirect($this->redirectPath());
    }

    /**
     * Check if user has pending team invitations
     *
     * @param string $email
     * @return TeamInvite|null
     */
    private function checkForTeamInvitation($email)
    {
        return TeamInvite::withoutGlobalScopes()
            ->where('email', $email)
            ->first();
    }

    /**
     * Handle email verification for users with team invitations
     *
     * @param \App\User $user
     * @param TeamInvite $teamInvitation
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleTeamInvitationVerification($user, $teamInvitation)
    {
        try {
            // Set the organization session for the invited user
            session(['organization_id' => $teamInvitation->organization_id]);

            // The UserLoginAt listener will handle the organization assignment
            // and permission transfer when the Login event is fired

            // Redirect directly to dashboard, skipping onboarding
            return redirect()->route('dashboard')->with('success', 'Welcome! You have been added to the organization.');

        } catch (\Exception $e) {
            Log::error('Error handling team invitation verification: ' . $e->getMessage());
            // Fallback to normal flow if something goes wrong
            return redirect($this->redirectPath());
        }
    }
}
