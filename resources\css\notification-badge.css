/* Notification Badge Styles */
/* Matches the existing Blade template styling */

.notification {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    position: absolute;
    border: 2px solid #ffffff; /* White border for better contrast */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Subtle shadow for depth */
}

.notification-danger {
    background-color: #ff4d4f; /* Ant Design danger color - more vibrant */
}

.sidebar-notification {
    top: -2px;
    right: -2px;
    z-index: 10;
}

/* Optional: Notification count styling (if showCount is enabled) */
.notification-count {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

/* Ensure proper positioning for menu items */
.ant-menu-item.relative {
    position: relative !important;
}

/* Animation for notification badge */
.notification {
    animation: pulse 2s infinite;
    /* Improve rendering quality */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    image-rendering: crisp-edges;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden; /* Prevent flickering */
}

@keyframes pulse {
    0% {
        transform: scale(1) translateZ(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.05) translateZ(0);
        opacity: 0.9;
    }
    100% {
        transform: scale(1) translateZ(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notification {
        width: 8px;
        height: 8px;
        border-width: 1px;
    }

    .sidebar-notification {
        top: -1px;
        right: -1px;
    }

    .notification-count {
        font-size: 8px;
        padding: 1px 4px;
        min-width: 12px;
    }
}
