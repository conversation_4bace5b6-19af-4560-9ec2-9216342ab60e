
<?php $__env->startSection('titles','Products'); ?>
<?php $__env->startSection('content'); ?>
    
    <div class="row row-cols-1 row-cols-lg-2">
        <div class="col d-flex flex-column align-items-start justify-content-center mb-4">
            <h2 class="mb-0"><?php echo e(trans('products.page_title')); ?></h2>
            <p class="mb-0"><?php echo e(trans('products.page_description')); ?></p>
        </div>
        <div class="col d-flex justify-content-lg-end justify-content-md-start mt-4 mt-lg-1 mt-1">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add_and_edit_product',[\App\Models\Organization\OrganizationUserPermission::class,auth()->user()->organization_id])): ?>
                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess', "product")): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-product', \App\Models\Product\Variant::query())): ?>
                    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary" data-bs-toggle="modal"
                    data-bs-target="#create_product"><?php echo e(trans('products.add_product_btn')); ?></a>
                        <?php else: ?>
                        <a href="javascript:void(0)"
                            id="disabled-button"
                            class="btn btn-primary float-lg-right float-md-right "
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            disabled
                            title="Please upgrade to access this feature">
                               <?php echo e(trans('products.add_product_btn')); ?>

                        </a>
                        <?php endif; ?>
                   <?php endif; ?>
                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('SubscriptionAccess',"import")): ?>
                <a href="<?php echo e(route("import.csv.step1")); ?>"
                   class="btn btn-outline-primary ms-2"><?php echo e(trans('products.bulk_import_btn')); ?></a>
                   <?php endif; ?>
            <?php endif; ?>

            
            <a href="<?php echo e(route("export.exportOne")); ?>" class="btn btn-outline-dark ms-2"><?php echo e(trans('products.export_btn')); ?></a>
            
        </div>
    </div>


    
    <!-- 
        <div>
            <ul class="p-0 row">
                
                    
                        <div class="col-main-progress-js col-12 col-md-6 col-lg-4 col-xl-3" data-batchId="">
                            <div class="main-progress-js mt-3">
                                <p class="mb-0 fs-12 fw-bold"></p>
                                <div class="d-flex align-items-center">
                                    <div class="progress" style="height: 1rem;width: 100%;">
                                        <div class="batch-progress-js progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: %"></div>
                                    </div>
                                    <div class="ms-3">
                                        <span class="status-publish p-1 px-2 rounded fs-12 batch-progress-number-js">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                   
                
            </ul>
        </div>
     -->


        
                    <input type="hidden" id="filter_total_product_count" value="0" />
                    <input type="hidden" class="filter_bulk_productIds" value="" />
                    <input type="hidden" id="filter_total_product_array" value="" />
                    <div id="listingTable" data-delete-product-route="<?php echo e(route('products.delete.json', ':id')); ?>" data-orgId="<?php echo e($org_id); ?>"></div>
                     <?php echo app('Illuminate\Foundation\Vite')("resources/js/components/productListing/ListingTable.jsx"); ?>
        


        
        <?php if (isset($component)) { $__componentOriginal42fe63e433b35bb50ad75ff7b387b537 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42fe63e433b35bb50ad75ff7b387b537 = $attributes; } ?>
<?php $component = App\View\Components\Products\AddProduct::resolve(['orgId' => ''.e($org_id).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.add-product'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\AddProduct::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42fe63e433b35bb50ad75ff7b387b537)): ?>
<?php $attributes = $__attributesOriginal42fe63e433b35bb50ad75ff7b387b537; ?>
<?php unset($__attributesOriginal42fe63e433b35bb50ad75ff7b387b537); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42fe63e433b35bb50ad75ff7b387b537)): ?>
<?php $component = $__componentOriginal42fe63e433b35bb50ad75ff7b387b537; ?>
<?php unset($__componentOriginal42fe63e433b35bb50ad75ff7b387b537); ?>
<?php endif; ?>


        <?php $__env->startPush('footer_scripts'); ?>
            <script>
                $(document).ready(function() {

                    /*
                     * Open product popup if their is any exception
                     * */
                    <?php $__errorArgs = ["sku"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    $("#create_product").modal("show");
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <?php $__errorArgs = ["name"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    $("#create_product").modal("show");
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                })
            </script>
        <?php $__env->stopPush(); ?>
        <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/index.blade.php ENDPATH**/ ?>