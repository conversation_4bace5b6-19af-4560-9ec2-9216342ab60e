<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\OrganizationRequest;
use App\Models\Organization\Organization;
use App\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OrganizationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (get_class($request->user()) == Organization::class) {
            $organizations = $request->user()->relatedOrganizations();
        } elseif (get_class($request->user()) == User::class) {
            $organizations = $request->user()->organizations();
        }
        return response([
            'message' => 'Organizations retrieved successfully',
            'organizations' => $organizations->orderBy('updated_at', 'desc')->get()
        ]);
    }
    public function renderOrganizationSelect()
    {
        return Inertia::render('organizationselect/OrganizationSelect', [
            'title' => 'Select Organization'
        ]);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    { {
        }
        if ($request->get('id')) {
            $org = $request->user()->relatedOrganizations()->findOrFail($request->get('id'));
        } else {
            $org = $this->get()->first();
        }
        session(
            [
                'organization_id' => $org->id,
            ]
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(OrganizationRequest $request)
    {
        if (get_class($request->user()) == User::class) {
            $user = $request->user();
            // $organization = $request->user()->organizations()->create($request->validated());
        } else {
            $user = $request->user()->user;
            // Create the organization
            // $organization = $request->user()->relatedOrganizations()->create($request->validated());
        }

        // // Assign data for seeding
        // $organization->data = $request->all();

        // // Call the method to seed default account data
        // $organization->seed_default_account_data($organization);


        // $controller = new AuthController();
        // $controller->loginOrganization($request);
        $organization = new Organization();
        return $organization->set_user($user)
            ->set_data($request->all())
            ->store(
                // when error
                function ($errors) {
                    return response([
                        'message' => 'Failed to create organization',
                        'errors' => $errors
                    ], 422);
                },

                // when success
                function ($obj) {
                    response([
                        'message' => 'Organization created su        ccessfully',
                        'oragnization' => $obj
                    ]);
                }
            );
        // response([
        //     'message' => 'Organization created su        ccessfully',
        //     'oragnization' => $organization
        // ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $org = $request->user()->relatedOrganizations()->findOrFail($id);
        return response([
            'message' => 'Organization retrieved successfully',
            'oragnization' => $org
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // $org = $request->user()->relatedOrganizations()->findOrFail($id);
        // $org->update($request->all());
        // return response([
        //     'message' => 'Organization updated successfully',
        //     'data' => $org
        // ]);
        if (get_class($request->user()) == User::class) {
            $user = $request->user();
        } else {
            $user = $request->user()->user;
        }
        $organization = new Organization();
        return $organization->set_user($user)
            ->set_data($request->all())
            ->store(
                // when error
                function ($errors) {
                    return response([
                        'message' => 'Failed to update organization',
                        'errors' => $errors
                    ], 422);
                },

                // when success
                function ($obj) {
                    response([
                        'message' => 'Organization updated su        ccessfully',
                        'oragnization' => $obj
                    ]);
                }
            );
    }

    /**
     * Get organization billing status.
     */
    public function billingStatus(Request $request)
    {
        $user = $request->user();
        $organization = Organization::where('id', $user->organization_id)->first();

        if (!$organization) {
            return response([
                'message' => 'Organization not found',
                'billing_status' => null
            ], 404);
        }

        $billingStatus = [
            'on_trial' => $organization->onTrial(),
            'is_subscribed' => $organization->is_subscribed(),
            'trial_ends_at' => $organization->trial_ends_at,
            'subscription_canceled' => false,
            'remaining_grace_period' => null,
            'days_left_in_trial' => null,
            'days_left_in_grace_period' => null,
        ];

        // Calculate days left in trial if on trial
        if ($billingStatus['on_trial'] && $organization->trial_ends_at) {
            $billingStatus['days_left_in_trial'] = now()->diffInDays(\Carbon\Carbon::parse($organization->trial_ends_at));
        }

        // Check if subscription is canceled
        if ($billingStatus['is_subscribed']) {
            $subscription = $organization->subscription('default');
            if ($subscription && $subscription->canceled()) {
                $billingStatus['subscription_canceled'] = true;
                $billingStatus['remaining_grace_period'] = $organization->remaining_grace_period();

                if ($billingStatus['remaining_grace_period']) {
                    $billingStatus['days_left_in_grace_period'] = now()->diffInDays(\Carbon\Carbon::parse($billingStatus['remaining_grace_period']));
                }
            }
        }

        return response([
            'message' => 'Organization billing status retrieved successfully',
            'billing_status' => $billingStatus
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // $org = $request->user()->organizations()->findOrFail($id);
        // $org->delete();
        // return response([
        //     'message' => 'Organization deleted successfully'
        // ]);
        return response([
            'message' => 'Organization delete not allowed'
        ]);
    }
}
