{"__meta": {"id": "01JYNYY915N3A37NCK03B7TZCY", "datetime": "2025-06-26 11:11:05", "utime": **********.766885, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[11:11:05] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.739231, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750936264.087332, "end": **********.766909, "duration": 1.6795768737792969, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750936264.087332, "relative_start": 0, "end": **********.653573, "relative_end": **********.653573, "duration": 1.****************, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.653598, "relative_start": 1.****************, "end": **********.766911, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.674938, "relative_start": 1.****************, "end": **********.682423, "relative_end": **********.682423, "duration": 0.007485151290893555, "duration_str": "7.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.758725, "relative_start": 1.****************, "end": **********.759324, "relative_end": **********.759324, "duration": 0.0005991458892822266, "duration_str": "599μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.762839, "relative_start": 1.****************, "end": **********.762989, "relative_end": **********.762989, "duration": 0.0001499652862548828, "duration_str": "150μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:182-219</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00594, "accumulated_duration_str": "5.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.707677, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 48.316}, {"sql": "select * from `channels` where `organization_id` = '1' and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7457678, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:186", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=186", "ajax": false, "filename": "DashboardController.php", "line": "186"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.316, "width_percent": 51.684}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "data": "array:5 [\n  \"file_path\" => null\n  \"input_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:5 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:6 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n          \"status\" => \"Product Status\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Export\"\n    \"nodes\" => []\n  ]\n  \"data_required\" => array:9 [\n    \"organization_id\" => \"1\"\n    \"output_type\" => \"export\"\n    \"template_method_type\" => \"export\"\n    \"apimio_plans_access\" => array:2 [\n      \"prime_user\" => true\n      \"template_save\" => true\n    ]\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.export\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"filter_query\" => array:2 [\n      \"filter_export_productIds\" => \"all\"\n      \"filter_total_product_array\" => null\n    ]\n  ]\n  \"template_attributes\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "uri": "GET api/2024-12/shopify-sync-status", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:182-219</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f3f296a-28e9-48b3-bca7-a45207be4a38\" target=\"_blank\">View in Telescope</a>", "duration": "1.68s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1312741963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1312741963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-150169107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-150169107\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-644569142 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InRXNHpEL2NDWUI2MlAycE8xblpGMHc9PSIsInZhbHVlIjoiS28yRzRTU3JFSXV5M2VkOGlyN2VmUzBoT0tlL2o5a2JHMXV0ZjYwVDdac2p3eTVuU01QL1U4VE81dVpGYzdmRWxZbnlvSUtidmJNd1BibS9pelNMYldqUG1TT0t4V2tuRnRVd2VNUEc0UGJENWp6eFFYZE56ZkNyTmR4d21seEsiLCJtYWMiOiI5OTE3NmZhZjliYThmYzJhN2UxZjM1Njk5NzIzNGEwNDE3N2I4MGM2NGJkOTUxYzI3OTNiYzFkMzZlMGUyMjU3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJORzV3YXNOSkthSUNCa0JUYWVZclE9PSIsInZhbHVlIjoiZzB1K1FaMUVBTHRvaGQreFNOd1hRTytSSnpTY1lTU1BxRVRpNGpEL00xbkM2ZUNOWFdhbFc5Z1NkSWVtMjdxV1Vtbm5XekxTTzl0RXlwRUhsL1VxWmJybkppQlUxMmRIY0xYaEVoR0t4VUN0V3BNM1NUZGRYMHdsQ1lKdDRRWXo4akE3Z0c3LzhlR1JVY3E5OWFBN1hnPT0iLCJtYWMiOiJhODM4ZGFjODBjZDk4YzBkMjMxYWFiMzdkYWE3MWQ5ZGVhYTEwNjU1MTNhOTFmNDZjNDllN2MxNTQ4YzM1YjJhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InRXNHpEL2NDWUI2MlAycE8xblpGMHc9PSIsInZhbHVlIjoiS28yRzRTU3JFSXV5M2VkOGlyN2VmUzBoT0tlL2o5a2JHMXV0ZjYwVDdac2p3eTVuU01QL1U4VE81dVpGYzdmRWxZbnlvSUtidmJNd1BibS9pelNMYldqUG1TT0t4V2tuRnRVd2VNUEc0UGJENWp6eFFYZE56ZkNyTmR4d21seEsiLCJtYWMiOiI5OTE3NmZhZjliYThmYzJhN2UxZjM1Njk5NzIzNGEwNDE3N2I4MGM2NGJkOTUxYzI3OTNiYzFkMzZlMGUyMjU3IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImZIdFhwL1IyQWxFeTRxenl4NUZHVFE9PSIsInZhbHVlIjoiT3UzN3UxVEFBMWg2dHlFZnNKa3hkOEtMWm9xT29rVXA2bXBuYTNPanpMN1RKKzVISG9BSnNjTWNyV3Z3WUFtbDlhbzdRR1RZSkYvdTNaUjFzeUFUYlZBM2dwNmhKT3Q5RnFMWTFvTDdvNTZEUUsyNklOUnJxcW1qREpnRVViaWgiLCJtYWMiOiJiZjg4MGRjNWYzYzQwZmE4OTExMTlkYzJhYTdkMzdhMTgxMzRiYWRhOTNkMWQ5MTUwYmE3NGQ5NmI4M2E4NTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644569142\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1143235466 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|8gBCKFx8JFW6g5Ej8uiEEmwDzamMqWJbYg7745moF1DDM6UqUlJmFTbMVxKE|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mkkZyW6FwGI2plywjexah8GhcGowWkkfsl8V58MO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143235466\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 11:11:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVlQ3cwR1FzOThlbWFOK0Zrd0RQdlE9PSIsInZhbHVlIjoiUWhyUUhSbDVLQmxDSWFZUXQvejB2OEdZdjc1RFc2eDFNVUdhTERYUitqYjJBOXlkZk5obzNwWmpLa0JVNU9kcHdtdUF5M0Y2TzNPbVB3a3drME5OMEU2Q243ejBUSFVDbFJnSDdCM3FQQjlGWTlyTExsK3dtVVk0R1JBUUY0RUYiLCJtYWMiOiI0OGNmZDA0MDUzNDNlZjg3MjcwN2QxMzVjODE0NjUwZmE3N2M3NjhmNDZhMGEyZTcwYTFjNzQzNzIzN2EzYTk5IiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 13:11:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Ikk5VS92SFhLT0diVTkyZnNHVStCYWc9PSIsInZhbHVlIjoialRUaEc3NE5wSUErYWFETXB3Wm9xNjhmZmt5L1RFMWFBMWRZTE9NNmRRTjZVMVJBVWkrOGl5WjJ4LzNxRkNzSzBGWTN5U2lDcHV4ZkR3UzUzRHJ0VCtKSlpFK1FxblJ6OVBaU2FXZWFyWmhJYzkyUUxMNlZzUjBlVlY2RzRQSG8iLCJtYWMiOiI0ZTUxNmRiZDJmZWNiYzRjZDVmMjFhMDRhYWQzMWRjYTMxODY5OGY0YjA0MmVhZDIyMzk3N2YxOTJkZDk4YTdiIiwidGFnIjoiIn0%3D; expires=Thu, 26 Jun 2025 13:11:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVlQ3cwR1FzOThlbWFOK0Zrd0RQdlE9PSIsInZhbHVlIjoiUWhyUUhSbDVLQmxDSWFZUXQvejB2OEdZdjc1RFc2eDFNVUdhTERYUitqYjJBOXlkZk5obzNwWmpLa0JVNU9kcHdtdUF5M0Y2TzNPbVB3a3drME5OMEU2Q243ejBUSFVDbFJnSDdCM3FQQjlGWTlyTExsK3dtVVk0R1JBUUY0RUYiLCJtYWMiOiI0OGNmZDA0MDUzNDNlZjg3MjcwN2QxMzVjODE0NjUwZmE3N2M3NjhmNDZhMGEyZTcwYTFjNzQzNzIzN2EzYTk5IiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 13:11:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Ikk5VS92SFhLT0diVTkyZnNHVStCYWc9PSIsInZhbHVlIjoialRUaEc3NE5wSUErYWFETXB3Wm9xNjhmZmt5L1RFMWFBMWRZTE9NNmRRTjZVMVJBVWkrOGl5WjJ4LzNxRkNzSzBGWTN5U2lDcHV4ZkR3UzUzRHJ0VCtKSlpFK1FxblJ6OVBaU2FXZWFyWmhJYzkyUUxMNlZzUjBlVlY2RzRQSG8iLCJtYWMiOiI0ZTUxNmRiZDJmZWNiYzRjZDVmMjFhMDRhYWQzMWRjYTMxODY5OGY0YjA0MmVhZDIyMzk3N2YxOTJkZDk4YTdiIiwidGFnIjoiIn0%3D; expires=Thu, 26-Jun-2025 13:11:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-401655058 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8HZKK1eleVzEhNBRaAFR8rDbg6aEXisvI4Jozd0j</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file_path</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Status</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Export</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => []\n    </samp>]\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n      \"<span class=sf-dump-key>apimio_plans_access</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>prime_user</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>template_save</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.export</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>filter_query</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>filter_export_productIds</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n        \"<span class=sf-dump-key>filter_total_product_array</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401655058\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus"}, "badge": null}}