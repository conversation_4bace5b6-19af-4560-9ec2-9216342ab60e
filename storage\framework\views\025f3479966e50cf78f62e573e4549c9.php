<!-- The Modal -->
<div class="modal fade" id="<?php echo e($id); ?>" tabindex="-1" aria-labelledby="example-<?php echo e($id); ?>" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <div>
                <h5 class="modal-title Poppins semibold" id="exampleModalLabel" style="font-size: 20px">
                    <?php echo e($header); ?>

                </h5>
                <small><?php echo e($message); ?></small>
                </div>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post"  action="<?php echo e($formUrl); ?>" id="rename-form">
                    <?php echo csrf_field(); ?>

                    <?php if(isset($folderId)): ?>
                        <input type="hidden" name="id" value="<?php echo e($folderId); ?>">
                    <?php endif; ?>

                    <div class="form-group mb-5">
                        <label class="mb-1"><?php echo e($title); ?></label>
                        <input type="text" class="form-control <?php $__errorArgs = ["folder_name"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="folder-name" name="folder_name" value="<?php echo e(isset($folderName) ? $folderName : old('folder_name')); ?>"
                               required>
                        <?php $__errorArgs = ["folder_name"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <input type="hidden" class="form-control" id="link" name="parent_link" value="<?php echo e($link); ?>" required>
                        <input type="hidden" class="form-control" id="folder_id" name="folder_id" value="<?php echo e(isset($parentFolderId) ? $parentFolderId : null); ?>">
                        <span class="text-danger" role="alert">
                            <small></small>
                        </span>
                    </div>
                    <div class="form-group " style="float: right !important; " >
                        <button type="submit" class="btn btn-primary btn-width ripplelink "><?php echo e($btnText); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\packages\apimio\gallery\src\Providers/../views/components/folder-create.blade.php ENDPATH**/ ?>