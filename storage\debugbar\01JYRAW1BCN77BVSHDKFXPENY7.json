{"__meta": {"id": "01JYRAW1BCN77BVSHDKFXPENY7", "datetime": "2025-06-27 09:18:04", "utime": **********.141491, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751015882.991306, "end": **********.141526, "duration": 1.1502199172973633, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1751015882.991306, "relative_start": 0, "end": **********.080397, "relative_end": **********.080397, "duration": 1.****************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.080418, "relative_start": 1.****************, "end": **********.141529, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "61.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.100878, "relative_start": 1.***************, "end": **********.105342, "relative_end": **********.105342, "duration": 0.004463911056518555, "duration_str": "4.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.129507, "relative_start": 1.****************, "end": **********.138772, "relative_end": **********.138772, "duration": 0.009264945983886719, "duration_str": "9.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x auth/Login", "param_count": null, "params": [], "start": **********.135891, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\js/Pages/auth/Login.jsxauth/Login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fjs%2FPages%2Fauth%2FLogin.jsx&line=1", "ajax": false, "filename": "Login.jsx", "line": "?"}, "render_count": 1, "name_original": "auth/Login"}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Api\\AuthController@loginForm<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/AuthController.php:32-37</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tKkih4EXNVPQLejRd9eganwOQBdUdlsllZTWWDbM", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/dashboard\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Api\\AuthController@loginForm", "uri": "GET login", "controller": "App\\Http\\Controllers\\Api\\AuthController@loginForm<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/AuthController.php:32-37</a>", "middleware": "web, guest", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4103fa-255f-4f18-844a-dcbe654368de\" target=\"_blank\">View in Telescope</a>", "duration": "1.15s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-227784361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-227784361\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1202884445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1202884445\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-110635381 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"987 characters\">twk_uuid_65d3e19f8d261e1b5f628230=%7B%22uuid%22%3A%221.HQ31DN2QCvCz1AEwIkTctvNQH8nLTyXMrdvndBo315Z7Fw05zg4FB4PEyU3amO9ryZIKTKYSrJmRd6tH5DqEtRIt6HUgSVAzCjoto%22%2C%22version%22%3A3%2C%22domain%22%3Anull%2C%22ts%22%3A1749829494742%7D; _clck=1mj3g60%7C2%7Cfx3%7C0%7C2000; XSRF-TOKEN=eyJpdiI6InJrK1pxMk5rSHBVTlZvRWIvcTU4NlE9PSIsInZhbHVlIjoiMGVDSXpGWERaYjFXUE42dWRIZC8vYUlmWFN4c05iTHhMSkNyQkdNL0FJekxWZ0RoSjJWTndGR2xvcHFxYkx6cW1BU3gzcWZOdlZzOVFISWVGOWNxQ2xoeXlOcCtnNUc1RXQvZGtFTUxMazVGVU1xM2s1RXlqWnVPbnVLNk5MYWciLCJtYWMiOiIyZmI0MWFkNThjOGM3NTdhNjY5OGY3ODVhNGE5Nzk4ODRiZDJkY2RmODhkN2YwOTU2NTY2ZWRhZTZhOTM1OWQwIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Ijg4amRMcEpxS1pnbUNOU3BoM0pKZ2c9PSIsInZhbHVlIjoiOGFxckNOakF1c210SDNJUEovOEJTMWtaSHlVRDlHZVNZNTlJUittODNXK2J0SzVScDJJUTBHVTBKcTVTc0Q2dVJFMFJsYVRZTGMrV0VLa1YvcFdmNTYzbmpkUlZuL1VaeExNNkxRVEVWdmNVWkZoaHpjdjlVR05EeHB6cU9VbEwiLCJtYWMiOiIzY2Y0Yzc0YWI5ZjQyMjNmNjUyOTAzNzg3YTBlY2QzMDkzOTJiODY1ODk3YzY2NjQ2M2FiYzA5Zjg2ZjRhNjNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110635381\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1451803665 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>twk_uuid_65d3e19f8d261e1b5f628230</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tKkih4EXNVPQLejRd9eganwOQBdUdlsllZTWWDbM</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ONj5ndkWvkHhzdTw40hUGjalmOauYfZxk5zLvJs4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451803665\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1228387940 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:18:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228387940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1404422945 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tKkih4EXNVPQLejRd9eganwOQBdUdlsllZTWWDbM</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404422945\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Api\\AuthController@loginForm"}, "badge": null}}